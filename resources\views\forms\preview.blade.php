<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $form->title }} - Preview</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div class="max-w-4xl mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-eye text-blue-600 text-sm"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-semibold text-gray-900">Form Preview</h1>
                        <p class="text-sm text-gray-600">{{ $form->title }}</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <i class="fas fa-eye mr-1"></i>
                        Preview Mode
                    </span>
                    <a href="{{ route('forms.builder', $form) }}" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                        <i class="fas fa-edit mr-2"></i>
                        Back to Builder
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Container -->
    <div class="max-w-2xl mx-auto py-8 px-4">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <!-- Form Header -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $form->title }}</h2>
                @if($form->description)
                    <p class="text-gray-600">{{ $form->description }}</p>
                @endif
            </div>

            <!-- Form Fields -->
            <form id="preview-form" class="space-y-6">
                @csrf
                <div id="form-fields-container">
                    <!-- Fields will be rendered here by JavaScript -->
                </div>

                <!-- Submit Button -->
                <div class="pt-6 border-t border-gray-200">
                    <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Submit Form
                    </button>
                    <p class="text-xs text-gray-500 mt-2 text-center">
                        <i class="fas fa-info-circle mr-1"></i>
                        This is a preview - submissions will not be saved
                    </p>
                </div>
            </form>
        </div>

        <!-- Preview Info -->
        <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-600"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Preview Mode</h3>
                    <p class="text-sm text-yellow-700 mt-1">
                        This is how your form will appear to users. Form submissions in preview mode will not be saved.
                        Go back to the builder to make changes or publish your form.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Form data from backend
        const formFields = @json($form->fields ?? []);
        
        document.addEventListener('DOMContentLoaded', function() {
            renderFormFields();
            
            // Handle form submission
            document.getElementById('preview-form').addEventListener('submit', function(e) {
                e.preventDefault();
                alert('This is a preview - form submission is disabled.');
            });
        });

        function renderFormFields() {
            const container = document.getElementById('form-fields-container');
            
            if (!formFields || formFields.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-12 text-gray-500">
                        <i class="fas fa-inbox text-4xl text-gray-300 mb-4"></i>
                        <p class="text-lg font-medium">No fields added yet</p>
                        <p class="text-sm">Go back to the builder to add fields to your form</p>
                    </div>
                `;
                return;
            }

            let html = '';
            formFields.forEach(field => {
                html += renderField(field);
            });
            
            container.innerHTML = html;
        }

        function renderField(field) {
            const fieldId = `field_${field.id || Math.random().toString(36).substr(2, 9)}`;
            const fieldName = field.name || fieldId;
            const label = field.label || field.type;
            const required = field.required ? 'required' : '';
            const requiredMark = field.required ? '<span class="text-red-500">*</span>' : '';

            switch(field.type) {
                case 'text':
                case 'email':
                case 'number':
                    return `
                        <div class="form-field">
                            <label for="${fieldId}" class="block text-sm font-medium text-gray-700 mb-2">
                                ${label} ${requiredMark}
                            </label>
                            <input type="${field.type}" 
                                   id="${fieldId}" 
                                   name="${fieldName}" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                                   placeholder="${field.placeholder || 'Enter ' + label.toLowerCase()}"
                                   ${required}>
                        </div>
                    `;
                
                case 'textarea':
                    return `
                        <div class="form-field">
                            <label for="${fieldId}" class="block text-sm font-medium text-gray-700 mb-2">
                                ${label} ${requiredMark}
                            </label>
                            <textarea id="${fieldId}" 
                                      name="${fieldName}" 
                                      rows="4" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                                      placeholder="${field.placeholder || 'Enter ' + label.toLowerCase()}"
                                      ${required}></textarea>
                        </div>
                    `;
                
                case 'select':
                    const options = field.options || ['Option 1', 'Option 2', 'Option 3'];
                    let optionsHtml = '<option value="">Choose an option</option>';
                    options.forEach(option => {
                        optionsHtml += `<option value="${option}">${option}</option>`;
                    });
                    
                    return `
                        <div class="form-field">
                            <label for="${fieldId}" class="block text-sm font-medium text-gray-700 mb-2">
                                ${label} ${requiredMark}
                            </label>
                            <select id="${fieldId}" 
                                    name="${fieldName}" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    ${required}>
                                ${optionsHtml}
                            </select>
                        </div>
                    `;
                
                case 'radio':
                    const radioOptions = field.options || ['Option 1', 'Option 2', 'Option 3'];
                    let radioHtml = '';
                    radioOptions.forEach((option, index) => {
                        const optionId = `${fieldId}_${index}`;
                        radioHtml += `
                            <div class="flex items-center">
                                <input id="${optionId}" 
                                       name="${fieldName}" 
                                       type="radio" 
                                       value="${option}" 
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                                       ${required}>
                                <label for="${optionId}" class="ml-3 text-sm text-gray-700">${option}</label>
                            </div>
                        `;
                    });
                    
                    return `
                        <div class="form-field">
                            <fieldset>
                                <legend class="block text-sm font-medium text-gray-700 mb-3">
                                    ${label} ${requiredMark}
                                </legend>
                                <div class="space-y-3">
                                    ${radioHtml}
                                </div>
                            </fieldset>
                        </div>
                    `;
                
                case 'checkbox':
                    const checkboxOptions = field.options || ['Option 1', 'Option 2', 'Option 3'];
                    let checkboxHtml = '';
                    checkboxOptions.forEach((option, index) => {
                        const optionId = `${fieldId}_${index}`;
                        checkboxHtml += `
                            <div class="flex items-center">
                                <input id="${optionId}" 
                                       name="${fieldName}[]" 
                                       type="checkbox" 
                                       value="${option}" 
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="${optionId}" class="ml-3 text-sm text-gray-700">${option}</label>
                            </div>
                        `;
                    });
                    
                    return `
                        <div class="form-field">
                            <fieldset>
                                <legend class="block text-sm font-medium text-gray-700 mb-3">
                                    ${label} ${requiredMark}
                                </legend>
                                <div class="space-y-3">
                                    ${checkboxHtml}
                                </div>
                            </fieldset>
                        </div>
                    `;
                
                case 'date':
                    return `
                        <div class="form-field">
                            <label for="${fieldId}" class="block text-sm font-medium text-gray-700 mb-2">
                                ${label} ${requiredMark}
                            </label>
                            <input type="date" 
                                   id="${fieldId}" 
                                   name="${fieldName}" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   ${required}>
                        </div>
                    `;
                
                default:
                    return `
                        <div class="form-field">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                ${label} ${requiredMark}
                            </label>
                            <div class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500">
                                ${field.type} field (preview not available)
                            </div>
                        </div>
                    `;
            }
        }
    </script>
</body>
</html>
