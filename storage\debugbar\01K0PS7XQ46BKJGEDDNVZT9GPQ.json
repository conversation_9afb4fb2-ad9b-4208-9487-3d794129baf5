{"__meta": {"id": "01K0PS7XQ46BKJGEDDNVZT9GPQ", "datetime": "2025-07-21 15:22:08", "utime": **********.485257, "method": "GET", "uri": "/forms", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 17, "start": **********.183008, "end": **********.485273, "duration": 0.302264928817749, "duration_str": "302ms", "measures": [{"label": "Booting", "start": **********.183008, "relative_start": 0, "end": **********.374756, "relative_end": **********.374756, "duration": 0.*****************, "duration_str": "192ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.374772, "relative_start": 0.*****************, "end": **********.485275, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "111ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.396394, "relative_start": 0.*****************, "end": **********.400752, "relative_end": **********.400752, "duration": 0.004358053207397461, "duration_str": "4.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.451527, "relative_start": 0.*****************, "end": **********.483274, "relative_end": **********.483274, "duration": 0.031746864318847656, "duration_str": "31.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: forms.index", "start": **********.453559, "relative_start": 0.****************, "end": **********.453559, "relative_end": **********.453559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: pagination::tailwind", "start": **********.466224, "relative_start": 0.****************, "end": **********.466224, "relative_end": **********.466224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.app", "start": **********.466671, "relative_start": 0.2836630344390869, "end": **********.466671, "relative_end": **********.466671, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.navigation", "start": **********.468471, "relative_start": 0.2854630947113037, "end": **********.468471, "relative_end": **********.468471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.474917, "relative_start": 0.29190897941589355, "end": **********.474917, "relative_end": **********.474917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.476859, "relative_start": 0.2938511371612549, "end": **********.476859, "relative_end": **********.476859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.478896, "relative_start": 0.29588794708251953, "end": **********.478896, "relative_end": **********.478896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.479486, "relative_start": 0.2964780330657959, "end": **********.479486, "relative_end": **********.479486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.480154, "relative_start": 0.2971460819244385, "end": **********.480154, "relative_end": **********.480154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown", "start": **********.480697, "relative_start": 0.29768896102905273, "end": **********.480697, "relative_end": **********.480697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.responsive-nav-link", "start": **********.481846, "relative_start": 0.29883813858032227, "end": **********.481846, "relative_end": **********.481846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.responsive-nav-link", "start": **********.48236, "relative_start": 0.29935193061828613, "end": **********.48236, "relative_end": **********.48236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.responsive-nav-link", "start": **********.48282, "relative_start": 0.29981207847595215, "end": **********.48282, "relative_end": **********.48282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 25201360, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.20.0", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 13, "nb_templates": 13, "templates": [{"name": "forms.index", "param_count": null, "params": [], "start": **********.453533, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/forms/index.blade.phpforms.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fforms%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "pagination::tailwind", "param_count": null, "params": [], "start": **********.466201, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination/resources/views/tailwind.blade.phppagination::tailwind", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2Fresources%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.46665, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.navigation", "param_count": null, "params": [], "start": **********.468452, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/layouts/navigation.blade.phplayouts.navigation", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Flayouts%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "components.nav-link", "param_count": null, "params": [], "start": **********.474898, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/nav-link.blade.phpcomponents.nav-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fnav-link.blade.php&line=1", "ajax": false, "filename": "nav-link.blade.php", "line": "?"}}, {"name": "components.nav-link", "param_count": null, "params": [], "start": **********.47684, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/nav-link.blade.phpcomponents.nav-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fnav-link.blade.php&line=1", "ajax": false, "filename": "nav-link.blade.php", "line": "?"}}, {"name": "components.nav-link", "param_count": null, "params": [], "start": **********.478877, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/nav-link.blade.phpcomponents.nav-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fnav-link.blade.php&line=1", "ajax": false, "filename": "nav-link.blade.php", "line": "?"}}, {"name": "components.dropdown-link", "param_count": null, "params": [], "start": **********.479468, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}}, {"name": "components.dropdown-link", "param_count": null, "params": [], "start": **********.480127, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}}, {"name": "components.dropdown", "param_count": null, "params": [], "start": **********.480669, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/dropdown.blade.phpcomponents.dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}}, {"name": "components.responsive-nav-link", "param_count": null, "params": [], "start": **********.481825, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/responsive-nav-link.blade.phpcomponents.responsive-nav-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fresponsive-nav-link.blade.php&line=1", "ajax": false, "filename": "responsive-nav-link.blade.php", "line": "?"}}, {"name": "components.responsive-nav-link", "param_count": null, "params": [], "start": **********.482341, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/responsive-nav-link.blade.phpcomponents.responsive-nav-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fresponsive-nav-link.blade.php&line=1", "ajax": false, "filename": "responsive-nav-link.blade.php", "line": "?"}}, {"name": "components.responsive-nav-link", "param_count": null, "params": [], "start": **********.482801, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/responsive-nav-link.blade.phpcomponents.responsive-nav-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fresponsive-nav-link.blade.php&line=1", "ajax": false, "filename": "responsive-nav-link.blade.php", "line": "?"}}]}, "queries": {"count": 11, "nb_statements": 10, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01304, "accumulated_duration_str": "13.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.416, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "formdb", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'nmNq7LuX2ea8Wa1tD7RrtDHSN8YpvKknrrbPD9cV' limit 1", "type": "query", "params": [], "bindings": ["nmNq7LuX2ea8Wa1tD7RrtDHSN8YpvKknrrbPD9cV"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.421513, "duration": 0.00579, "duration_str": "5.79ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "formdb", "explain": null, "start_percent": 0, "width_percent": 44.402}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.435519, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "formdb", "explain": null, "start_percent": 44.402, "width_percent": 5.752}, {"sql": "select count(*) as aggregate from `forms` where `forms`.`user_id` = 2 and `forms`.`user_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/FormController.php", "file": "D:\\Projects\\form-builder\\app\\Http\\Controllers\\FormController.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.4416769, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "FormController.php:21", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/FormController.php", "file": "D:\\Projects\\form-builder\\app\\Http\\Controllers\\FormController.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FHttp%2FControllers%2FFormController.php&line=21", "ajax": false, "filename": "FormController.php", "line": "21"}, "connection": "formdb", "explain": null, "start_percent": 50.153, "width_percent": 7.055}, {"sql": "select * from `forms` where `forms`.`user_id` = 2 and `forms`.`user_id` is not null order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/FormController.php", "file": "D:\\Projects\\form-builder\\app\\Http\\Controllers\\FormController.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.4435742, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "FormController.php:21", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/FormController.php", "file": "D:\\Projects\\form-builder\\app\\Http\\Controllers\\FormController.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FHttp%2FControllers%2FFormController.php&line=21", "ajax": false, "filename": "FormController.php", "line": "21"}, "connection": "formdb", "explain": null, "start_percent": 57.209, "width_percent": 3.604}, {"sql": "select * from `form_fields` where `form_fields`.`form_id` = 1 and `form_fields`.`form_id` is not null order by `order` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "forms.index", "file": "D:\\Projects\\form-builder\\resources\\views/forms/index.blade.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.459852, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "forms.index:51", "source": {"index": 20, "namespace": "view", "name": "forms.index", "file": "D:\\Projects\\form-builder\\resources\\views/forms/index.blade.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fforms%2Findex.blade.php&line=51", "ajax": false, "filename": "index.blade.php", "line": "51"}, "connection": "formdb", "explain": null, "start_percent": 60.813, "width_percent": 10.89}, {"sql": "select * from `form_submissions` where `form_submissions`.`form_id` = 1 and `form_submissions`.`form_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "forms.index", "file": "D:\\Projects\\form-builder\\resources\\views/forms/index.blade.php", "line": 52}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.462817, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "forms.index:52", "source": {"index": 20, "namespace": "view", "name": "forms.index", "file": "D:\\Projects\\form-builder\\resources\\views/forms/index.blade.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fforms%2Findex.blade.php&line=52", "ajax": false, "filename": "index.blade.php", "line": "52"}, "connection": "formdb", "explain": null, "start_percent": 71.702, "width_percent": 7.209}, {"sql": "select * from `translations` where exists (select * from `languages` where `translations`.`language_id` = `languages`.`id` and `code` = 'en') and `key` = 'app.name' limit 1", "type": "query", "params": [], "bindings": ["en", "app.name"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, {"index": 17, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "D:\\Projects\\form-builder\\app\\Helpers\\TranslationHelper.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.470854, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Translation.php:30", "source": {"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FTranslation.php&line=30", "ajax": false, "filename": "Translation.php", "line": "30"}, "connection": "formdb", "explain": null, "start_percent": 78.911, "width_percent": 7.132}, {"sql": "select * from `translations` where exists (select * from `languages` where `translations`.`language_id` = `languages`.`id` and `code` = 'en') and `key` = 'nav.dashboard' limit 1", "type": "query", "params": [], "bindings": ["en", "nav.dashboard"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, {"index": 17, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "D:\\Projects\\form-builder\\app\\Helpers\\TranslationHelper.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.47329, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Translation.php:30", "source": {"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FTranslation.php&line=30", "ajax": false, "filename": "Translation.php", "line": "30"}, "connection": "formdb", "explain": null, "start_percent": 86.043, "width_percent": 6.442}, {"sql": "select * from `translations` where exists (select * from `languages` where `translations`.`language_id` = `languages`.`id` and `code` = 'en') and `key` = 'nav.my_forms' limit 1", "type": "query", "params": [], "bindings": ["en", "nav.my_forms"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, {"index": 17, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "D:\\Projects\\form-builder\\app\\Helpers\\TranslationHelper.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.4756281, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Translation.php:30", "source": {"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FTranslation.php&line=30", "ajax": false, "filename": "Translation.php", "line": "30"}, "connection": "formdb", "explain": null, "start_percent": 92.485, "width_percent": 3.604}, {"sql": "select * from `translations` where exists (select * from `languages` where `translations`.`language_id` = `languages`.`id` and `code` = 'en') and `key` = 'nav.admin' limit 1", "type": "query", "params": [], "bindings": ["en", "nav.admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, {"index": 17, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "D:\\Projects\\form-builder\\app\\Helpers\\TranslationHelper.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.477621, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Translation.php:30", "source": {"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FTranslation.php&line=30", "ajax": false, "filename": "Translation.php", "line": "30"}, "connection": "formdb", "explain": null, "start_percent": 96.089, "width_percent": 3.911}]}, "models": {"data": {"App\\Models\\Translation": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FTranslation.php&line=1", "ajax": false, "filename": "Translation.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Form": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FForm.php&line=1", "ajax": false, "filename": "Form.php", "line": "?"}}, "App\\Models\\FormSubmission": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FFormSubmission.php&line=1", "ajax": false, "filename": "FormSubmission.php", "line": "?"}}}, "count": 7, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 7}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/forms", "action_name": "forms.index", "controller_action": "App\\Http\\Controllers\\FormController@index", "uri": "GET forms", "controller": "App\\Http\\Controllers\\FormController@index<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FHttp%2FControllers%2FFormController.php&line=19\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FHttp%2FControllers%2FFormController.php&line=19\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/FormController.php:19-23</a>", "middleware": "web, auth", "duration": "304ms", "peak_memory": "26MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1511570845 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1511570845\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1668209402 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1668209402\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1460766128 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/admin/forms</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ms;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1412 characters\">__stripe_mid=45fe3c7d-c6f2-457a-9c9f-a107d38d1d54978fc7; _ga=GA1.1.192285856.1746099575; _ga_69MPZE94D5=GS1.1.1746099574.1.1.1746099605.0.0.0; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndUUkRKeEd3elBPclVweVUrakhwcFE9PSIsInZhbHVlIjoiUHhxUmdma0t6RHltV2pCOWlEbmlOcUUrS2VBOCtRVjNrcklHYlpnc3lLL0dQeXlhV0VhNzh0Um5SakFGVitSdVh3bGw2WGg3MjM4NTU4aEhDQXlXRSt1MEtUN0xoQ2tHWFl2T2l3RDAwY0N6ZUlqQ3BSMDNrRnlCYnNSRnZEZG9wZlN0eVQ1SHE5dk5Nbk1CWlZ1bjByWXE1SFFjNVBiZHliU0U1MVgrbk1wcFdndzBrWis2VTcwRTdEMFJMcVdMNk9HSlVtTVJKZHNHTHhGZUEyQ29TbjQvMnJJSmFCT3AyUDJxRXFvQUp0Yz0iLCJtYWMiOiJlN2FmNmI4MWJjMDZlMjVmZWEyMjA3NDBkNGQxMTA2ODIzZjVkNjgzZTIwOTlmNTgyMWVkNTI1NjY3ZTAxZjc3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InJSSWtEcWJGcWI3OHNWZEFGYnFnRnc9PSIsInZhbHVlIjoidUdTeFdBQjhhWWlYbDk0RFpPQkRlN2Vhbjg5TXlqSDZ4L3hkVjN3TnZvY3lYTUZSanR0OE1mSDhpcVUrQXloQWc1N0U2VG9PRmY4R1ZqWk9hWHdiZ3NlTCtPdVR6OEMxS3hTWjZwaVNQUWlrbmh5bVRJbmFsajdvazl2U0tVcFMiLCJtYWMiOiI5YzcwMWIzZWYzMGNkZGVjZWM1MzY2YjkxOTQ1OTU5OTRmZmE4ZDUzMTdkYWU1NWY0ZjkwNWQ1OGE5ZWI4M2VlIiwidGFnIjoiIn0%3D; anggur_form_builder_session=eyJpdiI6IjEzK2pyUlJYTGMvLzdZdlVtUVNYSVE9PSIsInZhbHVlIjoiUE52eHp3N0dpQ1Y2SDNPeld5TEo2N3lQN1VySFRsdElmTjl6NGUxbDZrN1NNTkJDRW5TOUxaQmsyTE1CdDlCd2NFbDJZUzM3K0V5SFdvU0RlRFVvT0xqTnQvMVg0WitVOWN1cW5YbTdwN3pVdjJQSW5HdXJlNkFVVVJ2ZGlYREIiLCJtYWMiOiI3ZmJlNzU2NDJjY2M5OWQ1YTM2ODI1NGRhZGE3NDdmZTBkMDc4ZGZlMGIxMmQ0ZjUyMGQwM2E1NmNiYjAyZDYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1460766128\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-13899058 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_69MPZE94D5</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">n8tjAvFfC0SdTDeXFBvDP9ipLDa2OqtwPubmZtNX</span>\"\n  \"<span class=sf-dump-key>anggur_form_builder_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nmNq7LuX2ea8Wa1tD7RrtDHSN8YpvKknrrbPD9cV</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-13899058\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-532841565 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 15:22:08 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-532841565\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2020811620 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">n8tjAvFfC0SdTDeXFBvDP9ipLDa2OqtwPubmZtNX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2020811620\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/forms", "action_name": "forms.index", "controller_action": "App\\Http\\Controllers\\FormController@index"}, "badge": null}}