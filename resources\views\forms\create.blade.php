<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Create Form - {{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased bg-gray-50">
    <!-- Google Forms Style Header -->
    <div class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-3">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('dashboard') }}" class="flex items-center">
                        <div class="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-wpforms text-white text-lg"></i>
                        </div>
                        <div class="ml-3">
                            <h1 class="text-xl font-medium text-gray-900">Forms</h1>
                        </div>
                    </a>
                </div>
                <div class="flex items-center space-x-3">
                    <button id="save-form" class="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 disabled:opacity-50" disabled>
                        <i class="fas fa-save mr-2"></i>Save
                    </button>
                    <button id="preview-form" class="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition duration-200" disabled>
                        <i class="fas fa-eye mr-2"></i>Preview
                    </button>
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-medium">{{ substr(Auth::user()->name, 0, 1) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="min-h-screen">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

            <!-- Form Container -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">

                <!-- Form Header Section -->
                <div class="bg-gradient-to-r from-purple-600 to-purple-700 px-6 py-8">
                    <div class="space-y-4">
                        <!-- Form Title -->
                        <div>
                            <input type="text"
                                   id="form-title"
                                   placeholder="Untitled form"
                                   value="{{ old('title', request('template') ? ucfirst(request('template')) . ' Form' : '') }}"
                                   class="w-full bg-transparent text-white text-2xl font-medium placeholder-purple-200 border-none focus:outline-none focus:ring-0 p-0"
                                   style="background: none; box-shadow: none;">
                        </div>

                        <!-- Form Description -->
                        <div>
                            <textarea id="form-description"
                                      placeholder="Form description"
                                      rows="2"
                                      class="w-full bg-transparent text-purple-100 placeholder-purple-200 border-none focus:outline-none focus:ring-0 p-0 resize-none"
                                      style="background: none; box-shadow: none;">{{ old('description', request('template') ? 'Please fill out this ' . request('template') . ' form.' : '') }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- Form Builder Area -->
                <div class="p-6">
                    <!-- Quick Start Section -->
                    <div id="quick-start" class="text-center py-12">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-plus text-purple-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Add your first question</h3>
                        <p class="text-gray-600 mb-6">Choose a question type to get started</p>

                        <!-- Quick Field Types -->
                        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 max-w-2xl mx-auto">
                            <button class="field-type-btn p-4 bg-gray-50 hover:bg-purple-50 border border-gray-200 hover:border-purple-300 rounded-lg transition-all duration-200" data-type="text">
                                <i class="fas fa-font text-gray-600 text-lg mb-2"></i>
                                <div class="text-sm font-medium text-gray-900">Short Answer</div>
                            </button>
                            <button class="field-type-btn p-4 bg-gray-50 hover:bg-purple-50 border border-gray-200 hover:border-purple-300 rounded-lg transition-all duration-200" data-type="textarea">
                                <i class="fas fa-align-left text-gray-600 text-lg mb-2"></i>
                                <div class="text-sm font-medium text-gray-900">Paragraph</div>
                            </button>
                            <button class="field-type-btn p-4 bg-gray-50 hover:bg-purple-50 border border-gray-200 hover:border-purple-300 rounded-lg transition-all duration-200" data-type="select">
                                <i class="fas fa-list text-gray-600 text-lg mb-2"></i>
                                <div class="text-sm font-medium text-gray-900">Multiple Choice</div>
                            </button>
                            <button class="field-type-btn p-4 bg-gray-50 hover:bg-purple-50 border border-gray-200 hover:border-purple-300 rounded-lg transition-all duration-200" data-type="checkbox">
                                <i class="fas fa-check-square text-gray-600 text-lg mb-2"></i>
                                <div class="text-sm font-medium text-gray-900">Checkboxes</div>
                            </button>
                            <button class="field-type-btn p-4 bg-gray-50 hover:bg-purple-50 border border-gray-200 hover:border-purple-300 rounded-lg transition-all duration-200" data-type="email">
                                <i class="fas fa-envelope text-gray-600 text-lg mb-2"></i>
                                <div class="text-sm font-medium text-gray-900">Email</div>
                            </button>
                            <button class="field-type-btn p-4 bg-gray-50 hover:bg-purple-50 border border-gray-200 hover:border-purple-300 rounded-lg transition-all duration-200" data-type="number">
                                <i class="fas fa-hashtag text-gray-600 text-lg mb-2"></i>
                                <div class="text-sm font-medium text-gray-900">Number</div>
                            </button>
                            <button class="field-type-btn p-4 bg-gray-50 hover:bg-purple-50 border border-gray-200 hover:border-purple-300 rounded-lg transition-all duration-200" data-type="date">
                                <i class="fas fa-calendar text-gray-600 text-lg mb-2"></i>
                                <div class="text-sm font-medium text-gray-900">Date</div>
                            </button>
                            <button class="field-type-btn p-4 bg-gray-50 hover:bg-purple-50 border border-gray-200 hover:border-purple-300 rounded-lg transition-all duration-200" data-type="radio">
                                <i class="fas fa-dot-circle text-gray-600 text-lg mb-2"></i>
                                <div class="text-sm font-medium text-gray-900">Radio</div>
                            </button>
                        </div>
                    </div>

                    <!-- Form Fields Container -->
                    <div id="form-fields" class="space-y-4 hidden">
                        <!-- Fields will be added here dynamically -->
                    </div>

                    <!-- Add Field Button (shown after first field) -->
                    <div id="add-field-section" class="hidden mt-6 text-center">
                        <button id="add-field-btn" class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition duration-200">
                            <i class="fas fa-plus mr-2"></i>Add Question
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Google Forms-like functionality -->
    <script>
        let formFields = [];
        let fieldCounter = 0;
        let hasUnsavedChanges = false;

        // Initialize form creation
        document.addEventListener('DOMContentLoaded', function() {
            initializeFormCreation();
        });

        function initializeFormCreation() {
            // Handle template-based initialization
            const template = new URLSearchParams(window.location.search).get('template');
            if (template) {
                initializeTemplate(template);
            }

            // Add event listeners
            document.getElementById('form-title').addEventListener('input', handleFormChange);
            document.getElementById('form-description').addEventListener('input', handleFormChange);

            // Field type buttons
            document.querySelectorAll('.field-type-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    addField(this.dataset.type);
                });
            });

            // Save and preview buttons
            document.getElementById('save-form').addEventListener('click', saveForm);
            document.getElementById('preview-form').addEventListener('click', previewForm);
            document.getElementById('add-field-btn').addEventListener('click', showFieldTypeSelector);
        }

        function initializeTemplate(template) {
            const templates = {
                contact: {
                    fields: [
                        { type: 'text', label: 'Full Name', required: true },
                        { type: 'email', label: 'Email Address', required: true },
                        { type: 'text', label: 'Subject', required: false },
                        { type: 'textarea', label: 'Message', required: true }
                    ]
                },
                survey: {
                    fields: [
                        { type: 'select', label: 'How satisfied are you with our service?', options: ['Very Satisfied', 'Satisfied', 'Neutral', 'Dissatisfied', 'Very Dissatisfied'], required: true },
                        { type: 'textarea', label: 'Additional Comments', required: false }
                    ]
                },
                registration: {
                    fields: [
                        { type: 'text', label: 'First Name', required: true },
                        { type: 'text', label: 'Last Name', required: true },
                        { type: 'email', label: 'Email Address', required: true },
                        { type: 'text', label: 'Phone Number', required: false },
                        { type: 'date', label: 'Date of Birth', required: false }
                    ]
                },
                feedback: {
                    fields: [
                        { type: 'radio', label: 'Overall Rating', options: ['Excellent', 'Good', 'Average', 'Poor'], required: true },
                        { type: 'textarea', label: 'What did you like most?', required: false },
                        { type: 'textarea', label: 'What could be improved?', required: false }
                    ]
                },
                order: {
                    fields: [
                        { type: 'text', label: 'Product Name', required: true },
                        { type: 'number', label: 'Quantity', required: true },
                        { type: 'text', label: 'Customer Name', required: true },
                        { type: 'email', label: 'Email Address', required: true },
                        { type: 'textarea', label: 'Special Instructions', required: false }
                    ]
                }
            };

            if (templates[template]) {
                setTimeout(() => {
                    templates[template].fields.forEach(field => {
                        addField(field.type, field.label, field.required, field.options);
                    });
                }, 100);
            }
        }

        function addField(type, label = '', required = false, options = []) {
            fieldCounter++;
            const fieldId = `field_${fieldCounter}`;

            // Hide quick start section
            document.getElementById('quick-start').classList.add('hidden');
            document.getElementById('form-fields').classList.remove('hidden');
            document.getElementById('add-field-section').classList.remove('hidden');

            const fieldHtml = createFieldHtml(fieldId, type, label, required, options);
            document.getElementById('form-fields').insertAdjacentHTML('beforeend', fieldHtml);

            // Add field to array
            formFields.push({
                id: fieldId,
                type: type,
                label: label || getDefaultLabel(type),
                name: `field_${fieldCounter}`,
                required: required,
                options: options,
                order: fieldCounter
            });

            // Add event listeners to new field
            addFieldEventListeners(fieldId);
            handleFormChange();

            // Focus on the label input
            setTimeout(() => {
                const labelInput = document.querySelector(`#${fieldId} .field-label-input`);
                if (labelInput && !label) {
                    labelInput.focus();
                    labelInput.select();
                }
            }, 100);
        }

        function createFieldHtml(fieldId, type, label, required, options) {
            const defaultLabel = label || getDefaultLabel(type);

            return `
                <div id="${fieldId}" class="field-container bg-white border border-gray-200 rounded-lg p-6 hover:border-purple-300 transition-all duration-200">
                    <div class="flex justify-between items-start mb-4">
                        <div class="flex-1">
                            <input type="text"
                                   class="field-label-input text-lg font-medium text-gray-900 bg-transparent border-none focus:outline-none focus:ring-0 p-0 w-full"
                                   value="${defaultLabel}"
                                   placeholder="Question">
                        </div>
                        <div class="flex items-center space-x-2 ml-4">
                            <button class="duplicate-field text-gray-400 hover:text-gray-600 p-1" title="Duplicate">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="delete-field text-gray-400 hover:text-red-600 p-1" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>

                    <div class="field-preview mb-4">
                        ${createFieldPreview(type, options)}
                    </div>

                    <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                        <div class="flex items-center space-x-4">
                            <label class="flex items-center">
                                <input type="checkbox" class="field-required-checkbox" ${required ? 'checked' : ''}>
                                <span class="ml-2 text-sm text-gray-600">Required</span>
                            </label>
                        </div>
                        <div class="flex items-center space-x-2">
                            <select class="field-type-select text-sm border border-gray-300 rounded px-2 py-1">
                                <option value="text" ${type === 'text' ? 'selected' : ''}>Short Answer</option>
                                <option value="textarea" ${type === 'textarea' ? 'selected' : ''}>Paragraph</option>
                                <option value="email" ${type === 'email' ? 'selected' : ''}>Email</option>
                                <option value="number" ${type === 'number' ? 'selected' : ''}>Number</option>
                                <option value="date" ${type === 'date' ? 'selected' : ''}>Date</option>
                                <option value="select" ${type === 'select' ? 'selected' : ''}>Multiple Choice</option>
                                <option value="radio" ${type === 'radio' ? 'selected' : ''}>Radio</option>
                                <option value="checkbox" ${type === 'checkbox' ? 'selected' : ''}>Checkboxes</option>
                            </select>
                        </div>
                    </div>
                </div>
            `;
        }

        function createFieldPreview(type, options = []) {
            switch (type) {
                case 'text':
                case 'email':
                case 'number':
                    return '<input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="Your answer" disabled>';
                case 'textarea':
                    return '<textarea class="w-full px-3 py-2 border border-gray-300 rounded-md" rows="3" placeholder="Your answer" disabled></textarea>';
                case 'date':
                    return '<input type="date" class="px-3 py-2 border border-gray-300 rounded-md" disabled>';
                case 'select':
                    const selectOptions = options.length ? options : ['Option 1', 'Option 2', 'Option 3'];
                    return `
                        <select class="px-3 py-2 border border-gray-300 rounded-md" disabled>
                            <option>Choose</option>
                            ${selectOptions.map(opt => `<option>${opt}</option>`).join('')}
                        </select>
                    `;
                case 'radio':
                    const radioOptions = options.length ? options : ['Option 1', 'Option 2', 'Option 3'];
                    return `
                        <div class="space-y-2">
                            ${radioOptions.map((opt, i) => `
                                <label class="flex items-center">
                                    <input type="radio" name="preview_radio_${fieldCounter}" class="mr-2" disabled>
                                    <span>${opt}</span>
                                </label>
                            `).join('')}
                        </div>
                    `;
                case 'checkbox':
                    const checkboxOptions = options.length ? options : ['Option 1', 'Option 2', 'Option 3'];
                    return `
                        <div class="space-y-2">
                            ${checkboxOptions.map(opt => `
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-2" disabled>
                                    <span>${opt}</span>
                                </label>
                            `).join('')}
                        </div>
                    `;
                default:
                    return '<input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="Your answer" disabled>';
            }
        }

        function getDefaultLabel(type) {
            const labels = {
                text: 'Short answer text',
                textarea: 'Long answer text',
                email: 'Email address',
                number: 'Number',
                date: 'Date',
                select: 'Multiple choice question',
                radio: 'Multiple choice question',
                checkbox: 'Checkbox question'
            };
            return labels[type] || 'Question';
        }

        function addFieldEventListeners(fieldId) {
            const container = document.getElementById(fieldId);

            // Label input
            container.querySelector('.field-label-input').addEventListener('input', function() {
                updateFieldData(fieldId, 'label', this.value);
            });

            // Required checkbox
            container.querySelector('.field-required-checkbox').addEventListener('change', function() {
                updateFieldData(fieldId, 'required', this.checked);
            });

            // Type select
            container.querySelector('.field-type-select').addEventListener('change', function() {
                updateFieldType(fieldId, this.value);
            });

            // Delete button
            container.querySelector('.delete-field').addEventListener('click', function() {
                deleteField(fieldId);
            });

            // Duplicate button
            container.querySelector('.duplicate-field').addEventListener('click', function() {
                duplicateField(fieldId);
            });
        }

        function updateFieldData(fieldId, property, value) {
            const field = formFields.find(f => f.id === fieldId);
            if (field) {
                field[property] = value;
                handleFormChange();
            }
        }

        function updateFieldType(fieldId, newType) {
            const field = formFields.find(f => f.id === fieldId);
            if (field) {
                field.type = newType;

                // Update preview
                const container = document.getElementById(fieldId);
                const preview = container.querySelector('.field-preview');
                preview.innerHTML = createFieldPreview(newType);

                handleFormChange();
            }
        }

        function deleteField(fieldId) {
            if (confirm('Are you sure you want to delete this question?')) {
                document.getElementById(fieldId).remove();
                formFields = formFields.filter(f => f.id !== fieldId);

                // Show quick start if no fields left
                if (formFields.length === 0) {
                    document.getElementById('quick-start').classList.remove('hidden');
                    document.getElementById('form-fields').classList.add('hidden');
                    document.getElementById('add-field-section').classList.add('hidden');
                }

                handleFormChange();
            }
        }

        function duplicateField(fieldId) {
            const field = formFields.find(f => f.id === fieldId);
            if (field) {
                addField(field.type, field.label + ' (Copy)', field.required, field.options);
            }
        }

        function showFieldTypeSelector() {
            // For now, just add a text field. In a full implementation,
            // you might show a modal with field type options
            addField('text');
        }

        function handleFormChange() {
            hasUnsavedChanges = true;
            const saveBtn = document.getElementById('save-form');
            const previewBtn = document.getElementById('preview-form');

            const hasTitle = document.getElementById('form-title').value.trim();
            const hasFields = formFields.length > 0;

            if (hasTitle && hasFields) {
                saveBtn.disabled = false;
                previewBtn.disabled = false;
            } else {
                saveBtn.disabled = true;
                previewBtn.disabled = true;
            }
        }

        function saveForm() {
            const title = document.getElementById('form-title').value.trim();
            const description = document.getElementById('form-description').value.trim();

            if (!title) {
                alert('Please enter a form title');
                return;
            }

            if (formFields.length === 0) {
                alert('Please add at least one question');
                return;
            }

            // Show loading state
            const saveBtn = document.getElementById('save-form');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
            saveBtn.disabled = true;

            // Prepare form data
            const formData = {
                title: title,
                description: description,
                success_message: 'Thank you for your submission!',
                _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            };

            // Create form first
            fetch('{{ route("forms.store") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.form_id) {
                    // Now save the fields
                    const fieldsData = {
                        fields: formFields.map((field, index) => ({
                            type: field.type,
                            label: field.label,
                            name: `field_${index + 1}`,
                            required: field.required,
                            order: index + 1,
                            options: field.options,
                            placeholder: '',
                            help_text: '',
                            validation_rules: null,
                            settings: null
                        })),
                        _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    };

                    return fetch(`/forms/${data.form_id}/fields`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify(fieldsData)
                    });
                } else {
                    throw new Error(data.message || 'Failed to create form');
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    hasUnsavedChanges = false;
                    window.location.href = '{{ route("forms.index") }}';
                } else {
                    throw new Error(data.message || 'Failed to save fields');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error saving form: ' + error.message);
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;
            });
        }

        function previewForm() {
            // For now, just show an alert. In a full implementation,
            // you might open a modal or new tab with form preview
            alert('Preview functionality will be available after saving the form.');
        }

        // Warn about unsaved changes
        window.addEventListener('beforeunload', function(e) {
            if (hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    </script>
</body>
</html>
