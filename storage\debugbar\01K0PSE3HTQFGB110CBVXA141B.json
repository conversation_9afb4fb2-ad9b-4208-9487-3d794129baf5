{"__meta": {"id": "01K0PSE3HTQFGB110CBVXA141B", "datetime": "2025-07-21 15:25:31", "utime": **********.06677, "method": "GET", "uri": "/profile", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 42, "start": 1753111526.863336, "end": **********.066785, "duration": 4.203449010848999, "duration_str": "4.2s", "measures": [{"label": "Booting", "start": 1753111526.863336, "relative_start": 0, "end": **********.040592, "relative_end": **********.040592, "duration": 0.*****************, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.040606, "relative_start": 0.*****************, "end": **********.066786, "relative_end": 9.5367431640625e-07, "duration": 4.***************, "duration_str": "4.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.061499, "relative_start": 0.*****************, "end": **********.064154, "relative_end": **********.064154, "duration": 0.0026547908782958984, "duration_str": "2.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.112383, "relative_start": 0.*****************, "end": **********.06507, "relative_end": **********.06507, "duration": 3.****************, "duration_str": "3.95s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: profile.edit", "start": **********.114613, "relative_start": 0.*****************, "end": **********.114613, "relative_end": **********.114613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: profile.partials.update-profile-information-form", "start": **********.297645, "relative_start": 0.****************, "end": **********.297645, "relative_end": **********.297645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-label", "start": 1753111528.507497, "relative_start": 1.6441609859466553, "end": 1753111528.507497, "relative_end": 1753111528.507497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.text-input", "start": 1753111528.581237, "relative_start": 1.7179009914398193, "end": 1753111528.581237, "relative_end": 1753111528.581237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": 1753111528.650737, "relative_start": 1.7874009609222412, "end": 1753111528.650737, "relative_end": 1753111528.650737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-label", "start": 1753111528.734805, "relative_start": 1.8714690208435059, "end": 1753111528.734805, "relative_end": 1753111528.734805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.text-input", "start": 1753111528.735246, "relative_start": 1.8719098567962646, "end": 1753111528.735246, "relative_end": 1753111528.735246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": 1753111528.735586, "relative_start": 1.8722498416900635, "end": 1753111528.735586, "relative_end": 1753111528.735586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.primary-button", "start": 1753111528.736027, "relative_start": 1.8726909160614014, "end": 1753111528.736027, "relative_end": 1753111528.736027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: profile.partials.update-password-form", "start": 1753111528.775138, "relative_start": 1.911801815032959, "end": 1753111528.775138, "relative_end": 1753111528.775138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-label", "start": 1753111529.819256, "relative_start": 2.9559199810028076, "end": 1753111529.819256, "relative_end": 1753111529.819256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.text-input", "start": 1753111529.819678, "relative_start": 2.9563419818878174, "end": 1753111529.819678, "relative_end": 1753111529.819678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": 1753111529.819979, "relative_start": 2.9566428661346436, "end": 1753111529.819979, "relative_end": 1753111529.819979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-label", "start": 1753111529.820359, "relative_start": 2.9570229053497314, "end": 1753111529.820359, "relative_end": 1753111529.820359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.text-input", "start": 1753111529.820872, "relative_start": 2.957535982131958, "end": 1753111529.820872, "relative_end": 1753111529.820872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": 1753111529.821474, "relative_start": 2.9581379890441895, "end": 1753111529.821474, "relative_end": 1753111529.821474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-label", "start": 1753111529.822613, "relative_start": 2.9592769145965576, "end": 1753111529.822613, "relative_end": 1753111529.822613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.text-input", "start": 1753111529.823076, "relative_start": 2.959739923477173, "end": 1753111529.823076, "relative_end": 1753111529.823076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": 1753111529.823513, "relative_start": 2.960176944732666, "end": 1753111529.823513, "relative_end": 1753111529.823513, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.primary-button", "start": 1753111529.823874, "relative_start": 2.960537910461426, "end": 1753111529.823874, "relative_end": 1753111529.823874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: profile.partials.delete-user-form", "start": 1753111529.824264, "relative_start": 2.960927963256836, "end": 1753111529.824264, "relative_end": 1753111529.824264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.danger-button", "start": 1753111530.91352, "relative_start": 4.050184011459351, "end": 1753111530.91352, "relative_end": 1753111530.91352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-label", "start": 1753111530.948754, "relative_start": 4.085417985916138, "end": 1753111530.948754, "relative_end": 1753111530.948754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.text-input", "start": 1753111530.949202, "relative_start": 4.0858659744262695, "end": 1753111530.949202, "relative_end": 1753111530.949202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": 1753111530.949494, "relative_start": 4.08615779876709, "end": 1753111530.949494, "relative_end": 1753111530.949494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.secondary-button", "start": 1753111530.949903, "relative_start": 4.086566925048828, "end": 1753111530.949903, "relative_end": 1753111530.949903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.danger-button", "start": 1753111530.981769, "relative_start": 4.118432998657227, "end": 1753111530.981769, "relative_end": 1753111530.981769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.modal", "start": 1753111530.982207, "relative_start": 4.118870973587036, "end": 1753111530.982207, "relative_end": 1753111530.982207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.app", "start": **********.049942, "relative_start": 4.186605930328369, "end": **********.049942, "relative_end": **********.049942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.navigation", "start": **********.051777, "relative_start": 4.188440799713135, "end": **********.051777, "relative_end": **********.051777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.058666, "relative_start": 4.195329904556274, "end": **********.058666, "relative_end": **********.058666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.060635, "relative_start": 4.197299003601074, "end": **********.060635, "relative_end": **********.060635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.062788, "relative_start": 4.199451923370361, "end": **********.062788, "relative_end": **********.062788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.063222, "relative_start": 4.199885845184326, "end": **********.063222, "relative_end": **********.063222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown", "start": **********.063459, "relative_start": 4.200122833251953, "end": **********.063459, "relative_end": **********.063459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.responsive-nav-link", "start": **********.06398, "relative_start": 4.200644016265869, "end": **********.06398, "relative_end": **********.06398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.responsive-nav-link", "start": **********.064365, "relative_start": 4.201028823852539, "end": **********.064365, "relative_end": **********.064365, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.responsive-nav-link", "start": **********.064708, "relative_start": 4.201371908187866, "end": **********.064708, "relative_end": **********.064708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 25536848, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.20.0", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 38, "nb_templates": 38, "templates": [{"name": "profile.edit", "param_count": null, "params": [], "start": **********.114589, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/profile/edit.blade.phpprofile.edit", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fprofile%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}}, {"name": "profile.partials.update-profile-information-form", "param_count": null, "params": [], "start": **********.297628, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/profile/partials/update-profile-information-form.blade.phpprofile.partials.update-profile-information-form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fprofile%2Fpartials%2Fupdate-profile-information-form.blade.php&line=1", "ajax": false, "filename": "update-profile-information-form.blade.php", "line": "?"}}, {"name": "components.input-label", "param_count": null, "params": [], "start": 1753111528.507459, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/input-label.blade.phpcomponents.input-label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Finput-label.blade.php&line=1", "ajax": false, "filename": "input-label.blade.php", "line": "?"}}, {"name": "components.text-input", "param_count": null, "params": [], "start": 1753111528.581209, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/text-input.blade.phpcomponents.text-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Ftext-input.blade.php&line=1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": 1753111528.650713, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php&line=1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.input-label", "param_count": null, "params": [], "start": 1753111528.734781, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/input-label.blade.phpcomponents.input-label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Finput-label.blade.php&line=1", "ajax": false, "filename": "input-label.blade.php", "line": "?"}}, {"name": "components.text-input", "param_count": null, "params": [], "start": 1753111528.735226, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/text-input.blade.phpcomponents.text-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Ftext-input.blade.php&line=1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": 1753111528.735566, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php&line=1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.primary-button", "param_count": null, "params": [], "start": 1753111528.736006, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/primary-button.blade.phpcomponents.primary-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fprimary-button.blade.php&line=1", "ajax": false, "filename": "primary-button.blade.php", "line": "?"}}, {"name": "profile.partials.update-password-form", "param_count": null, "params": [], "start": 1753111528.775104, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/profile/partials/update-password-form.blade.phpprofile.partials.update-password-form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fprofile%2Fpartials%2Fupdate-password-form.blade.php&line=1", "ajax": false, "filename": "update-password-form.blade.php", "line": "?"}}, {"name": "components.input-label", "param_count": null, "params": [], "start": 1753111529.819226, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/input-label.blade.phpcomponents.input-label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Finput-label.blade.php&line=1", "ajax": false, "filename": "input-label.blade.php", "line": "?"}}, {"name": "components.text-input", "param_count": null, "params": [], "start": 1753111529.81966, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/text-input.blade.phpcomponents.text-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Ftext-input.blade.php&line=1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": 1753111529.819962, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php&line=1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.input-label", "param_count": null, "params": [], "start": 1753111529.820338, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/input-label.blade.phpcomponents.input-label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Finput-label.blade.php&line=1", "ajax": false, "filename": "input-label.blade.php", "line": "?"}}, {"name": "components.text-input", "param_count": null, "params": [], "start": 1753111529.820848, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/text-input.blade.phpcomponents.text-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Ftext-input.blade.php&line=1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": 1753111529.821445, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php&line=1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.input-label", "param_count": null, "params": [], "start": 1753111529.822592, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/input-label.blade.phpcomponents.input-label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Finput-label.blade.php&line=1", "ajax": false, "filename": "input-label.blade.php", "line": "?"}}, {"name": "components.text-input", "param_count": null, "params": [], "start": 1753111529.823049, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/text-input.blade.phpcomponents.text-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Ftext-input.blade.php&line=1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": 1753111529.823489, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php&line=1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.primary-button", "param_count": null, "params": [], "start": 1753111529.823856, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/primary-button.blade.phpcomponents.primary-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fprimary-button.blade.php&line=1", "ajax": false, "filename": "primary-button.blade.php", "line": "?"}}, {"name": "profile.partials.delete-user-form", "param_count": null, "params": [], "start": 1753111529.824246, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/profile/partials/delete-user-form.blade.phpprofile.partials.delete-user-form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fprofile%2Fpartials%2Fdelete-user-form.blade.php&line=1", "ajax": false, "filename": "delete-user-form.blade.php", "line": "?"}}, {"name": "components.danger-button", "param_count": null, "params": [], "start": 1753111530.91349, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/danger-button.blade.phpcomponents.danger-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fdanger-button.blade.php&line=1", "ajax": false, "filename": "danger-button.blade.php", "line": "?"}}, {"name": "components.input-label", "param_count": null, "params": [], "start": 1753111530.948727, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/input-label.blade.phpcomponents.input-label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Finput-label.blade.php&line=1", "ajax": false, "filename": "input-label.blade.php", "line": "?"}}, {"name": "components.text-input", "param_count": null, "params": [], "start": 1753111530.949173, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/text-input.blade.phpcomponents.text-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Ftext-input.blade.php&line=1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": 1753111530.949478, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php&line=1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.secondary-button", "param_count": null, "params": [], "start": 1753111530.949886, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/secondary-button.blade.phpcomponents.secondary-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fsecondary-button.blade.php&line=1", "ajax": false, "filename": "secondary-button.blade.php", "line": "?"}}, {"name": "components.danger-button", "param_count": null, "params": [], "start": 1753111530.981742, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/danger-button.blade.phpcomponents.danger-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fdanger-button.blade.php&line=1", "ajax": false, "filename": "danger-button.blade.php", "line": "?"}}, {"name": "components.modal", "param_count": null, "params": [], "start": 1753111530.982184, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/modal.blade.phpcomponents.modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fmodal.blade.php&line=1", "ajax": false, "filename": "modal.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.049913, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.navigation", "param_count": null, "params": [], "start": **********.051751, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/layouts/navigation.blade.phplayouts.navigation", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Flayouts%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "components.nav-link", "param_count": null, "params": [], "start": **********.058649, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/nav-link.blade.phpcomponents.nav-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fnav-link.blade.php&line=1", "ajax": false, "filename": "nav-link.blade.php", "line": "?"}}, {"name": "components.nav-link", "param_count": null, "params": [], "start": **********.060618, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/nav-link.blade.phpcomponents.nav-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fnav-link.blade.php&line=1", "ajax": false, "filename": "nav-link.blade.php", "line": "?"}}, {"name": "components.dropdown-link", "param_count": null, "params": [], "start": **********.062772, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}}, {"name": "components.dropdown-link", "param_count": null, "params": [], "start": **********.063206, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}}, {"name": "components.dropdown", "param_count": null, "params": [], "start": **********.063393, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/dropdown.blade.phpcomponents.dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}}, {"name": "components.responsive-nav-link", "param_count": null, "params": [], "start": **********.063965, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/responsive-nav-link.blade.phpcomponents.responsive-nav-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fresponsive-nav-link.blade.php&line=1", "ajax": false, "filename": "responsive-nav-link.blade.php", "line": "?"}}, {"name": "components.responsive-nav-link", "param_count": null, "params": [], "start": **********.06435, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/responsive-nav-link.blade.phpcomponents.responsive-nav-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fresponsive-nav-link.blade.php&line=1", "ajax": false, "filename": "responsive-nav-link.blade.php", "line": "?"}}, {"name": "components.responsive-nav-link", "param_count": null, "params": [], "start": **********.064693, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/components/responsive-nav-link.blade.phpcomponents.responsive-nav-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fcomponents%2Fresponsive-nav-link.blade.php&line=1", "ajax": false, "filename": "responsive-nav-link.blade.php", "line": "?"}}]}, "queries": {"count": 9, "nb_statements": 8, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.022160000000000003, "accumulated_duration_str": "22.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.076698, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "formdb", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'nmNq7LuX2ea8Wa1tD7RrtDHSN8YpvKknrrbPD9cV' limit 1", "type": "query", "params": [], "bindings": ["nmNq7LuX2ea8Wa1tD7RrtDHSN8YpvKknrrbPD9cV"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.081781, "duration": 0.013900000000000001, "duration_str": "13.9ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "formdb", "explain": null, "start_percent": 0, "width_percent": 62.726}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.1021159, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "formdb", "explain": null, "start_percent": 62.726, "width_percent": 5.054}, {"sql": "select * from `forms` where `forms`.`user_id` = 2 and `forms`.`user_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "profile.edit", "file": "D:\\Projects\\form-builder\\resources\\views/profile/edit.blade.php", "line": 63}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.291382, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "profile.edit:63", "source": {"index": 20, "namespace": "view", "name": "profile.edit", "file": "D:\\Projects\\form-builder\\resources\\views/profile/edit.blade.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fprofile%2Fedit.blade.php&line=63", "ajax": false, "filename": "edit.blade.php", "line": "63"}, "connection": "formdb", "explain": null, "start_percent": 67.78, "width_percent": 14.666}, {"sql": "select * from `form_submissions` where `form_submissions`.`form_id` = 1 and `form_submissions`.`form_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "profile.edit", "file": "D:\\Projects\\form-builder\\resources\\views/profile/edit.blade.php", "line": 75}, {"index": 24, "namespace": "view", "name": "profile.edit", "file": "D:\\Projects\\form-builder\\resources\\views/profile/edit.blade.php", "line": 75}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.295756, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "profile.edit:75", "source": {"index": 20, "namespace": "view", "name": "profile.edit", "file": "D:\\Projects\\form-builder\\resources\\views/profile/edit.blade.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fprofile%2Fedit.blade.php&line=75", "ajax": false, "filename": "edit.blade.php", "line": "75"}, "connection": "formdb", "explain": null, "start_percent": 82.446, "width_percent": 2.031}, {"sql": "select * from `translations` where exists (select * from `languages` where `translations`.`language_id` = `languages`.`id` and `code` = 'en') and `key` = 'app.name' limit 1", "type": "query", "params": [], "bindings": ["en", "app.name"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, {"index": 17, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "D:\\Projects\\form-builder\\app\\Helpers\\TranslationHelper.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.054631, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "Translation.php:30", "source": {"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FTranslation.php&line=30", "ajax": false, "filename": "Translation.php", "line": "30"}, "connection": "formdb", "explain": null, "start_percent": 84.477, "width_percent": 6.679}, {"sql": "select * from `translations` where exists (select * from `languages` where `translations`.`language_id` = `languages`.`id` and `code` = 'en') and `key` = 'nav.dashboard' limit 1", "type": "query", "params": [], "bindings": ["en", "nav.dashboard"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, {"index": 17, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "D:\\Projects\\form-builder\\app\\Helpers\\TranslationHelper.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.057282, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Translation.php:30", "source": {"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FTranslation.php&line=30", "ajax": false, "filename": "Translation.php", "line": "30"}, "connection": "formdb", "explain": null, "start_percent": 91.155, "width_percent": 2.978}, {"sql": "select * from `translations` where exists (select * from `languages` where `translations`.`language_id` = `languages`.`id` and `code` = 'en') and `key` = 'nav.my_forms' limit 1", "type": "query", "params": [], "bindings": ["en", "nav.my_forms"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, {"index": 17, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "D:\\Projects\\form-builder\\app\\Helpers\\TranslationHelper.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.059315, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Translation.php:30", "source": {"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FTranslation.php&line=30", "ajax": false, "filename": "Translation.php", "line": "30"}, "connection": "formdb", "explain": null, "start_percent": 94.134, "width_percent": 2.978}, {"sql": "select * from `translations` where exists (select * from `languages` where `translations`.`language_id` = `languages`.`id` and `code` = 'en') and `key` = 'nav.admin' limit 1", "type": "query", "params": [], "bindings": ["en", "nav.admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, {"index": 17, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "D:\\Projects\\form-builder\\app\\Helpers\\TranslationHelper.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.0611172, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Translation.php:30", "source": {"index": 16, "namespace": null, "name": "app/Models/Translation.php", "file": "D:\\Projects\\form-builder\\app\\Models\\Translation.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FTranslation.php&line=30", "ajax": false, "filename": "Translation.php", "line": "30"}, "connection": "formdb", "explain": null, "start_percent": 97.112, "width_percent": 2.888}]}, "models": {"data": {"App\\Models\\Translation": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FTranslation.php&line=1", "ajax": false, "filename": "Translation.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Form": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FForm.php&line=1", "ajax": false, "filename": "Form.php", "line": "?"}}, "App\\Models\\FormSubmission": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FFormSubmission.php&line=1", "ajax": false, "filename": "FormSubmission.php", "line": "?"}}}, "count": 7, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 7}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/profile", "action_name": "profile.edit", "controller_action": "App\\Http\\Controllers\\ProfileController@edit", "uri": "GET profile", "controller": "App\\Http\\Controllers\\ProfileController@edit<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FHttp%2FControllers%2FProfileController.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FHttp%2FControllers%2FProfileController.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/ProfileController.php:17-22</a>", "middleware": "web, auth", "duration": "4.2s", "peak_memory": "26MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-299050519 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-299050519\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1168630230 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1168630230\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1557076350 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ms;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1412 characters\">__stripe_mid=45fe3c7d-c6f2-457a-9c9f-a107d38d1d54978fc7; _ga=GA1.1.192285856.1746099575; _ga_69MPZE94D5=GS1.1.1746099574.1.1.1746099605.0.0.0; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndUUkRKeEd3elBPclVweVUrakhwcFE9PSIsInZhbHVlIjoiUHhxUmdma0t6RHltV2pCOWlEbmlOcUUrS2VBOCtRVjNrcklHYlpnc3lLL0dQeXlhV0VhNzh0Um5SakFGVitSdVh3bGw2WGg3MjM4NTU4aEhDQXlXRSt1MEtUN0xoQ2tHWFl2T2l3RDAwY0N6ZUlqQ3BSMDNrRnlCYnNSRnZEZG9wZlN0eVQ1SHE5dk5Nbk1CWlZ1bjByWXE1SFFjNVBiZHliU0U1MVgrbk1wcFdndzBrWis2VTcwRTdEMFJMcVdMNk9HSlVtTVJKZHNHTHhGZUEyQ29TbjQvMnJJSmFCT3AyUDJxRXFvQUp0Yz0iLCJtYWMiOiJlN2FmNmI4MWJjMDZlMjVmZWEyMjA3NDBkNGQxMTA2ODIzZjVkNjgzZTIwOTlmNTgyMWVkNTI1NjY3ZTAxZjc3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkRiN1ZPNHk1MDBGN1I4aGc3TSszdXc9PSIsInZhbHVlIjoiTm9kWnJ1aWlyd2REa3hLTndHM3VmS0hFY09jTzI1ejh2YzhUUUtCTmJnRU1WRWNpbCtoQWFxRFk1TkRwZnIrcHR0UnN6czJDWC8yb2lsSU1yZ2Z3aXRkUm80MEZzVzU4SGE1SUxhbkRIa1l3VzRESHFYRW4xYWFNYUlxU3Y1TFEiLCJtYWMiOiI0MjJhODZkM2RlMWQyYjBlNzY4YjFjNzc0ZjVhYjRlZDVhNmIyMTBkY2ZhOWNmZWZlMDI1ODdiYjkzMWYwZDM5IiwidGFnIjoiIn0%3D; anggur_form_builder_session=eyJpdiI6Imtld2d1aWRCZ1ZaamFDaVpTb1IxTmc9PSIsInZhbHVlIjoickswejZUbGpNU2plSnAzdUVoWkUyNDVCLzNETTcvcCtEQUlRRHhDMDZmVVFaWFd2RkNKclk2NUZsbG40amJLY3QwVHhlWGprRCtUZlF5bjQ4enhJVHF0UGFVbDNvbkFNcFNXVDA5RjJPdS9QODQrZGhxbHpmMTZUbVlPbXd3TzciLCJtYWMiOiIwYzYzNGZhNDdlNzZkYjhiMTQ0M2ZkNTNhMjJiNWVkMDBkNzMzYmMzMzc0NGM3MDA3ZWU1NjNiMmY4M2FiMWNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1557076350\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1238823857 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_69MPZE94D5</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">n8tjAvFfC0SdTDeXFBvDP9ipLDa2OqtwPubmZtNX</span>\"\n  \"<span class=sf-dump-key>anggur_form_builder_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nmNq7LuX2ea8Wa1tD7RrtDHSN8YpvKknrrbPD9cV</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1238823857\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2133355112 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 15:25:27 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2133355112\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1803672914 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">n8tjAvFfC0SdTDeXFBvDP9ipLDa2OqtwPubmZtNX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/admin/forms</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1803672914\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/profile", "action_name": "profile.edit", "controller_action": "App\\Http\\Controllers\\ProfileController@edit"}, "badge": null}}