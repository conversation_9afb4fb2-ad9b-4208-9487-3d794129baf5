@extends('layouts.admin')

@section('title', 'Admin Dashboard')
@section('page-title', 'Dashboard')

@section('breadcrumb')
    <li class="breadcrumb-item active">Dashboard</li>
@endsection

@section('content')
    <!-- Enhanced Info boxes with animations and better styling -->
    <div class="row">
        <div class="col-12 col-sm-6 col-md-3">
            <div class="info-box bg-gradient-info elevation-2">
                <span class="info-box-icon"><i class="fas fa-users"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Total Users</span>
                    <span class="info-box-number">
                        <span class="counter">{{ $stats['total_users'] }}</span>
                    </span>
                    <div class="progress">
                        <div class="progress-bar" style="width: 70%"></div>
                    </div>
                    <span class="progress-description">
                        <i class="fas fa-arrow-up text-success"></i> 12% increase from last month
                    </span>
                </div>
            </div>
        </div>

        <div class="col-12 col-sm-6 col-md-3">
            <div class="info-box bg-gradient-danger elevation-2">
                <span class="info-box-icon"><i class="fas fa-wpforms"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Total Forms</span>
                    <span class="info-box-number">
                        <span class="counter">{{ $stats['total_forms'] }}</span>
                    </span>
                    <div class="progress">
                        <div class="progress-bar" style="width: 85%"></div>
                    </div>
                    <span class="progress-description">
                        <i class="fas fa-arrow-up text-success"></i> 8% increase from last month
                    </span>
                </div>
            </div>
        </div>

        <div class="col-12 col-sm-6 col-md-3">
            <div class="info-box bg-gradient-success elevation-2">
                <span class="info-box-icon"><i class="fas fa-check-circle"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Active Forms</span>
                    <span class="info-box-number">
                        <span class="counter">{{ $stats['active_forms'] }}</span>
                    </span>
                    <div class="progress">
                        <div class="progress-bar" style="width: 92%"></div>
                    </div>
                    <span class="progress-description">
                        <i class="fas fa-arrow-up text-success"></i> 15% increase from last month
                    </span>
                </div>
            </div>
        </div>

        <div class="col-12 col-sm-6 col-md-3">
            <div class="info-box bg-gradient-warning elevation-2">
                <span class="info-box-icon"><i class="fas fa-paper-plane"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">Total Submissions</span>
                    <span class="info-box-number">
                        <span class="counter">{{ $stats['total_submissions'] }}</span>
                    </span>
                    <div class="progress">
                        <div class="progress-bar" style="width: 78%"></div>
                    </div>
                    <span class="progress-description">
                        <i class="fas fa-arrow-up text-success"></i> 25% increase from last month
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Stats Row -->
    <div class="row">
        <div class="col-12 col-sm-6 col-md-3">
            <div class="small-box bg-primary">
                <div class="inner">
                    <h3>{{ $stats['submissions_today'] }}</h3>
                    <p>Today's Submissions</p>
                </div>
                <div class="icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <a href="{{ route('admin.submissions.index') }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>

        <div class="col-12 col-sm-6 col-md-3">
            <div class="small-box bg-secondary">
                <div class="inner">
                    <h3>{{ $stats['submissions_this_week'] }}</h3>
                    <p>This Week's Submissions</p>
                </div>
                <div class="icon">
                    <i class="fas fa-calendar-week"></i>
                </div>
                <a href="{{ route('admin.submissions.index') }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>

        <div class="col-12 col-sm-6 col-md-3">
            <div class="small-box bg-teal">
                <div class="inner">
                    <h3>{{ number_format(($stats['active_forms'] / max($stats['total_forms'], 1)) * 100, 1) }}%</h3>
                    <p>Forms Active Rate</p>
                </div>
                <div class="icon">
                    <i class="fas fa-percentage"></i>
                </div>
                <a href="{{ route('admin.forms.index') }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>

        <div class="col-12 col-sm-6 col-md-3">
            <div class="small-box bg-indigo">
                <div class="inner">
                    <h3>{{ number_format($stats['total_submissions'] / max($stats['total_forms'], 1), 1) }}</h3>
                    <p>Avg Submissions per Form</p>
                </div>
                <div class="icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <a href="{{ route('admin.forms.index') }}" class="small-box-footer">
                    More info <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- Enhanced Charts and Analytics -->
    <div class="row">
        <div class="col-md-8">
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-line mr-1"></i>
                        Submission Analytics
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                        <button type="button" class="btn btn-tool" data-card-widget="maximize">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-responsive">
                                <canvas id="submissionChart" height="150"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="row">
                                <div class="col-6">
                                    <div class="description-block border-right">
                                        <span class="description-percentage text-success">
                                            <i class="fas fa-caret-up"></i> 17%
                                        </span>
                                        <h5 class="description-header">{{ number_format($stats['submissions_today']) }}</h5>
                                        <span class="description-text">TODAY</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="description-block">
                                        <span class="description-percentage text-info">
                                            <i class="fas fa-caret-up"></i> 23%
                                        </span>
                                        <h5 class="description-header">{{ number_format($stats['submissions_this_week']) }}</h5>
                                        <span class="description-text">THIS WEEK</span>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-6">
                                    <div class="description-block border-right">
                                        <span class="description-percentage text-warning">
                                            <i class="fas fa-caret-left"></i> 0%
                                        </span>
                                        <h5 class="description-header">{{ number_format($stats['submissions_this_week'] * 4) }}</h5>
                                        <span class="description-text">THIS MONTH</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="description-block">
                                        <span class="description-percentage text-danger">
                                            <i class="fas fa-caret-down"></i> 18%
                                        </span>
                                        <h5 class="description-header">{{ number_format($stats['total_submissions']) }}</h5>
                                        <span class="description-text">TOTAL</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card card-success card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-rocket mr-1"></i>
                        Quick Actions
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 mb-3">
                            <a href="{{ route('forms.create') }}" class="btn btn-primary btn-block btn-lg">
                                <i class="fas fa-plus mr-2"></i> Create New Form
                            </a>
                        </div>
                        <div class="col-12 mb-3">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-info btn-block">
                                <i class="fas fa-users mr-2"></i> Manage Users
                            </a>
                        </div>
                        <div class="col-12 mb-3">
                            <a href="{{ route('admin.forms.index') }}" class="btn btn-success btn-block">
                                <i class="fas fa-wpforms mr-2"></i> All Forms
                            </a>
                        </div>
                        <div class="col-12 mb-3">
                            <a href="{{ route('admin.submissions.index') }}" class="btn btn-warning btn-block">
                                <i class="fas fa-paper-plane mr-2"></i> All Submissions
                            </a>
                        </div>
                        <div class="col-12">
                            <a href="{{ route('admin.translations.index') }}" class="btn btn-secondary btn-block">
                                <i class="fas fa-language mr-2"></i> Translations
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Status Card -->
            <div class="card card-info card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-server mr-1"></i>
                        System Status
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <div class="progress-group">
                                Database
                                <span class="float-right"><b>{{ number_format($stats['total_forms'] + $stats['total_submissions']) }}</b>/∞</span>
                                <div class="progress progress-sm">
                                    <div class="progress-bar bg-success" style="width: 80%"></div>
                                </div>
                            </div>
                            <div class="progress-group">
                                Storage
                                <span class="float-right"><b>2.1</b>/10 GB</span>
                                <div class="progress progress-sm">
                                    <div class="progress-bar bg-info" style="width: 21%"></div>
                                </div>
                            </div>
                            <div class="progress-group">
                                Memory Usage
                                <span class="float-right"><b>512</b>/1024 MB</span>
                                <div class="progress progress-sm">
                                    <div class="progress-bar bg-warning" style="width: 50%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Recent Activity -->
    <div class="row">
        <div class="col-md-6">
            <div class="card card-warning card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-wpforms mr-1"></i>
                        Recent Forms
                    </h3>
                    <div class="card-tools">
                        <span class="badge badge-warning">{{ count($recent_forms) }}</span>
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                        <a href="{{ route('admin.forms.index') }}" class="btn btn-tool" title="View All">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <ul class="products-list product-list-in-card pl-2 pr-2">
                        @forelse($recent_forms as $form)
                            <li class="item">
                                <div class="product-img">
                                    <div class="img-circle elevation-1 d-flex align-items-center justify-content-center"
                                         style="width: 50px; height: 50px; background: {{ $form->is_active ? 'linear-gradient(45deg, #28a745, #20c997)' : 'linear-gradient(45deg, #6c757d, #adb5bd)' }};">
                                        <i class="fas fa-wpforms text-white"></i>
                                    </div>
                                </div>
                                <div class="product-info">
                                    <a href="{{ route('forms.builder', $form) }}" class="product-title">
                                        {{ $form->title }}
                                        <span class="badge {{ $form->is_active ? 'badge-success' : 'badge-secondary' }} float-right">
                                            {{ $form->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </a>
                                    <span class="product-description">
                                        <i class="fas fa-user text-muted mr-1"></i>{{ $form->user->name }}
                                        <i class="fas fa-clock text-muted ml-2 mr-1"></i>{{ $form->created_at->diffForHumans() }}
                                        <i class="fas fa-list text-muted ml-2 mr-1"></i>{{ $form->fields->count() }} fields
                                        <i class="fas fa-paper-plane text-muted ml-2 mr-1"></i>{{ $form->submissions->count() }} submissions
                                    </span>
                                </div>
                            </li>
                        @empty
                            <li class="item">
                                <div class="product-info text-center py-4">
                                    <i class="fas fa-wpforms text-muted fa-3x mb-3"></i>
                                    <p class="text-muted">No forms created yet.</p>
                                    <a href="{{ route('forms.create') }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-plus mr-1"></i>Create First Form
                                    </a>
                                </div>
                            </li>
                        @endforelse
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card card-danger card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-paper-plane mr-1"></i>
                        Recent Submissions
                    </h3>
                    <div class="card-tools">
                        <span class="badge badge-danger">{{ count($recent_submissions) }}</span>
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                        <a href="{{ route('admin.submissions.index') }}" class="btn btn-tool" title="View All">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <ul class="products-list product-list-in-card pl-2 pr-2">
                        @forelse($recent_submissions as $submission)
                            <li class="item">
                                <div class="product-img">
                                    <div class="img-circle elevation-1 d-flex align-items-center justify-content-center"
                                         style="width: 50px; height: 50px; background: linear-gradient(45deg, #dc3545, #fd7e14);">
                                        <i class="fas fa-paper-plane text-white"></i>
                                    </div>
                                </div>
                                <div class="product-info">
                                    <a href="{{ route('forms.submissions', $submission->form) }}" class="product-title">
                                        {{ $submission->form->title }}
                                        <span class="badge badge-primary float-right">#{{ $submission->id }}</span>
                                    </a>
                                    <span class="product-description">
                                        <i class="fas fa-clock text-muted mr-1"></i>{{ $submission->created_at->diffForHumans() }}
                                        <i class="fas fa-globe text-muted ml-2 mr-1"></i>{{ $submission->ip_address ?? 'Unknown IP' }}
                                        @if($submission->user_agent)
                                            <i class="fas fa-desktop text-muted ml-2 mr-1"></i>{{ Str::limit($submission->user_agent, 30) }}
                                        @endif
                                    </span>
                                </div>
                            </li>
                        @empty
                            <li class="item">
                                <div class="product-info text-center py-4">
                                    <i class="fas fa-paper-plane text-muted fa-3x mb-3"></i>
                                    <p class="text-muted">No submissions yet.</p>
                                    <small class="text-muted">Submissions will appear here once forms start receiving responses.</small>
                                </div>
                            </li>
                        @endforelse
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Admin Tools Row -->
    <div class="row">
        <div class="col-md-4">
            <div class="card card-secondary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-tools mr-1"></i>
                        Admin Tools
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-2">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-outline-primary btn-block btn-sm">
                                <i class="fas fa-users-cog"></i><br>User Management
                            </a>
                        </div>
                        <div class="col-6 mb-2">
                            <a href="{{ route('admin.forms.index') }}" class="btn btn-outline-success btn-block btn-sm">
                                <i class="fas fa-wpforms"></i><br>Form Management
                            </a>
                        </div>
                        <div class="col-6 mb-2">
                            <a href="{{ route('admin.submissions.index') }}" class="btn btn-outline-warning btn-block btn-sm">
                                <i class="fas fa-database"></i><br>Data Export
                            </a>
                        </div>
                        <div class="col-6 mb-2">
                            <a href="{{ route('admin.translations.index') }}" class="btn btn-outline-info btn-block btn-sm">
                                <i class="fas fa-language"></i><br>Translations
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card card-info card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-pie mr-1"></i>
                        Form Types Distribution
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <canvas id="formTypesChart" height="200"></canvas>
                        </div>
                        <div class="col-md-6">
                            <div class="progress-group">
                                Contact Forms
                                <span class="float-right"><b>160</b>/200</span>
                                <div class="progress progress-sm">
                                    <div class="progress-bar bg-primary" style="width: 80%"></div>
                                </div>
                            </div>
                            <div class="progress-group">
                                Survey Forms
                                <span class="float-right"><b>310</b>/400</span>
                                <div class="progress progress-sm">
                                    <div class="progress-bar bg-danger" style="width: 75%"></div>
                                </div>
                            </div>
                            <div class="progress-group">
                                Registration Forms
                                <span class="float-right"><b>480</b>/800</span>
                                <div class="progress progress-sm">
                                    <div class="progress-bar bg-success" style="width: 60%"></div>
                                </div>
                            </div>
                            <div class="progress-group">
                                Feedback Forms
                                <span class="float-right"><b>250</b>/500</span>
                                <div class="progress progress-sm">
                                    <div class="progress-bar bg-warning" style="width: 50%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    .counter {
        animation: countUp 2s ease-out;
    }

    @keyframes countUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .info-box {
        transition: all 0.3s ease;
    }

    .info-box:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .small-box {
        transition: all 0.3s ease;
    }

    .small-box:hover {
        transform: translateY(-3px);
    }
</style>
@endpush

@push('scripts')
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    $(document).ready(function() {
        // Counter animation
        $('.counter').each(function() {
            var $this = $(this);
            var countTo = $this.text();

            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'swing',
                step: function() {
                    $this.text(Math.floor(this.countNum).toLocaleString());
                },
                complete: function() {
                    $this.text(parseInt(countTo).toLocaleString());
                }
            });
        });

        // Submission Chart
        var submissionCtx = document.getElementById('submissionChart').getContext('2d');
        var submissionChart = new Chart(submissionCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
                datasets: [{
                    label: 'Submissions',
                    data: [65, 59, 80, 81, 56, 55, 40],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    }
                }
            }
        });

        // Form Types Chart
        var formTypesCtx = document.getElementById('formTypesChart').getContext('2d');
        var formTypesChart = new Chart(formTypesCtx, {
            type: 'doughnut',
            data: {
                labels: ['Contact Forms', 'Survey Forms', 'Registration Forms', 'Feedback Forms'],
                datasets: [{
                    data: [160, 310, 480, 250],
                    backgroundColor: [
                        '#007bff',
                        '#dc3545',
                        '#28a745',
                        '#ffc107'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });

        // Auto refresh stats every 5 minutes
        setInterval(function() {
            // AJAX call to refresh stats
            $.get('{{ route("admin.dashboard.stats") }}', function(data) {
                // Update counters with new data
                $('.counter').each(function(index) {
                    var newValue = data.stats[$(this).data('stat')];
                    if (newValue !== undefined) {
                        $(this).text(parseInt(newValue).toLocaleString());
                    }
                });
            }).fail(function() {
                console.log('Failed to refresh stats');
            });
        }, 300000); // 5 minutes

        // Tooltip initialization
        $('[data-toggle="tooltip"]').tooltip();

        // Card widget functionality
        $('[data-card-widget="collapse"]').on('click', function() {
            var card = $(this).closest('.card');
            var body = card.find('.card-body');

            if (body.is(':visible')) {
                body.slideUp();
                $(this).find('i').removeClass('fa-minus').addClass('fa-plus');
            } else {
                body.slideDown();
                $(this).find('i').removeClass('fa-plus').addClass('fa-minus');
            }
        });

        // Maximize card functionality
        $('[data-card-widget="maximize"]').on('click', function() {
            var card = $(this).closest('.card');

            if (card.hasClass('maximized-card')) {
                card.removeClass('maximized-card');
                $(this).find('i').removeClass('fa-compress').addClass('fa-expand');
                $('body').removeClass('maximized-card');
            } else {
                card.addClass('maximized-card');
                $(this).find('i').removeClass('fa-expand').addClass('fa-compress');
                $('body').addClass('maximized-card');
            }
        });
    });
</script>
@endpush
