<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>My Forms - {{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased bg-gray-50">

    <!-- Google Forms Style Header -->
    <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('dashboard') }}" class="flex items-center">
                        <div class="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-wpforms text-white text-lg"></i>
                        </div>
                        <div class="ml-3">
                            <h1 class="text-xl font-medium text-gray-900">Forms</h1>
                        </div>
                    </a>
                </div>
                <div class="flex items-center space-x-3">
                    <!-- Search -->
                    <div class="relative">
                        <input type="text" id="search-forms" placeholder="Search forms..." class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>

                    <!-- View Toggle -->
                    <div class="flex items-center bg-gray-100 rounded-lg p-1">
                        <button id="grid-view" class="p-2 rounded-md bg-white shadow-sm text-gray-700">
                            <i class="fas fa-th"></i>
                        </button>
                        <button id="list-view" class="p-2 rounded-md text-gray-500 hover:text-gray-700">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>

                    <!-- Create Button -->
                    <a href="{{ route('forms.create') }}" class="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                        <i class="fas fa-plus mr-2"></i>Create
                    </a>

                    <!-- User Avatar -->
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-medium">{{ substr(Auth::user()->name, 0, 1) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="min-h-screen bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

            <!-- Success Message -->
            @if(session('success'))
                <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-green-700">{{ session('success') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Forms Grid -->
            @if($forms->count() > 0)
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-medium text-gray-900">Your forms</h2>
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            <span>{{ $forms->total() }} forms</span>
                            <span>•</span>
                            <span>{{ $forms->where('is_active', true)->count() }} active</span>
                        </div>
                    </div>

                    <div id="forms-grid" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                        @foreach($forms as $form)
                            <div class="form-card group relative" data-form-title="{{ strtolower($form->title) }}">
                                <a href="{{ route('forms.builder', $form) }}" class="block">
                                    <div class="bg-white rounded-lg border border-gray-200 hover:border-purple-300 hover:shadow-md transition-all duration-200 p-4">
                                        <!-- Form Icon/Preview -->
                                        <div class="w-full h-32 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg mb-3 flex items-center justify-center relative overflow-hidden">
                                            <div class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-purple-600/10"></div>
                                            <i class="fas fa-wpforms text-purple-400 text-3xl relative z-10"></i>

                                            <!-- Status indicator -->
                                            <div class="absolute top-2 right-2">
                                                @if($form->is_active)
                                                    <div class="w-2 h-2 bg-green-500 rounded-full" title="Active"></div>
                                                @else
                                                    <div class="w-2 h-2 bg-gray-400 rounded-full" title="Inactive"></div>
                                                @endif
                                            </div>

                                            <!-- Form type badge -->
                                            <div class="absolute bottom-2 left-2">
                                                <span class="px-2 py-1 text-xs bg-white/80 text-gray-600 rounded-full">
                                                    {{ $form->fields->count() }} fields
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Form Info -->
                                        <div class="space-y-1">
                                            <h3 class="font-medium text-gray-900 text-sm truncate group-hover:text-purple-600">
                                                {{ $form->title }}
                                            </h3>
                                            <p class="text-xs text-gray-500">
                                                {{ $form->submissions->count() }} responses
                                            </p>
                                            <p class="text-xs text-gray-400">
                                                {{ $form->updated_at->diffForHumans() }}
                                            </p>
                                        </div>
                                    </div>
                                </a>

                                <!-- Quick Actions Menu -->
                                <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                    <div class="relative">
                                        <button class="p-1 text-gray-400 hover:text-gray-600 bg-white rounded-full shadow-sm" onclick="toggleMenu(event, 'menu-{{ $form->id }}')">
                                            <i class="fas fa-ellipsis-v text-xs"></i>
                                        </button>
                                        <div id="menu-{{ $form->id }}" class="hidden absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                                            <div class="py-1">
                                                <a href="{{ route('forms.builder', $form) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i class="fas fa-edit mr-2"></i>Edit
                                                </a>
                                                <a href="{{ route('forms.submissions', $form) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i class="fas fa-chart-bar mr-2"></i>Responses ({{ $form->submissions->count() }})
                                                </a>
                                                <a href="{{ route('form.show', $form->slug) }}" target="_blank" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i class="fas fa-external-link-alt mr-2"></i>Preview
                                                </a>
                                                <div class="border-t border-gray-100"></div>
                                                <button onclick="duplicateForm('{{ $form->id }}')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i class="fas fa-copy mr-2"></i>Duplicate
                                                </button>
                                                <button onclick="toggleFormStatus('{{ $form->id }}', {{ $form->is_active ? 'false' : 'true' }})" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i class="fas fa-{{ $form->is_active ? 'pause' : 'play' }} mr-2"></i>{{ $form->is_active ? 'Deactivate' : 'Activate' }}
                                                </button>
                                                <div class="border-t border-gray-100"></div>
                                                <form action="{{ route('forms.destroy', $form) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this form?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                                        <i class="fas fa-trash mr-2"></i>Delete
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    @if($forms->hasPages())
                        <div class="mt-8 flex justify-center">
                            {{ $forms->links() }}
                        </div>
                    @endif
                </div>
            @else
                <!-- Empty State -->
                <div class="text-center py-16">
                    <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-wpforms text-gray-400 text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-medium text-gray-900 mb-2">Create your first form</h3>
                    <p class="text-gray-500 mb-8 max-w-sm mx-auto">Get started by creating a form from scratch or choose from our templates.</p>

                    <!-- Quick Start Templates -->
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 max-w-2xl mx-auto mb-8">
                        <a href="{{ route('forms.create') }}" class="group p-4 bg-white rounded-lg border border-gray-200 hover:border-purple-300 hover:shadow-md transition-all duration-200">
                            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-plus text-white"></i>
                            </div>
                            <h4 class="text-sm font-medium text-gray-900 group-hover:text-purple-600">Blank Form</h4>
                        </a>

                        <a href="{{ route('forms.create') }}?template=contact" class="group p-4 bg-white rounded-lg border border-gray-200 hover:border-purple-300 hover:shadow-md transition-all duration-200">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-envelope text-white"></i>
                            </div>
                            <h4 class="text-sm font-medium text-gray-900 group-hover:text-purple-600">Contact</h4>
                        </a>

                        <a href="{{ route('forms.create') }}?template=survey" class="group p-4 bg-white rounded-lg border border-gray-200 hover:border-purple-300 hover:shadow-md transition-all duration-200">
                            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-poll text-white"></i>
                            </div>
                            <h4 class="text-sm font-medium text-gray-900 group-hover:text-purple-600">Survey</h4>
                        </a>

                        <a href="{{ route('forms.create') }}?template=feedback" class="group p-4 bg-white rounded-lg border border-gray-200 hover:border-purple-300 hover:shadow-md transition-all duration-200">
                            <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-comment text-white"></i>
                            </div>
                            <h4 class="text-sm font-medium text-gray-900 group-hover:text-purple-600">Feedback</h4>
                        </a>
                    </div>

                    <a href="{{ route('forms.create') }}" class="inline-flex items-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition duration-200">
                        <i class="fas fa-plus mr-2"></i>Create your first form
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- JavaScript for enhanced functionality -->
    <script>
        // Search functionality
        document.getElementById('search-forms').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const formCards = document.querySelectorAll('.form-card');

            formCards.forEach(card => {
                const title = card.dataset.formTitle;
                if (title.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });

        // View toggle functionality
        document.getElementById('grid-view').addEventListener('click', function() {
            document.getElementById('forms-grid').className = 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4';
            this.classList.add('bg-white', 'shadow-sm');
            this.classList.remove('text-gray-500');
            document.getElementById('list-view').classList.remove('bg-white', 'shadow-sm');
            document.getElementById('list-view').classList.add('text-gray-500');
        });

        document.getElementById('list-view').addEventListener('click', function() {
            document.getElementById('forms-grid').className = 'grid grid-cols-1 gap-2';
            this.classList.add('bg-white', 'shadow-sm');
            this.classList.remove('text-gray-500');
            document.getElementById('grid-view').classList.remove('bg-white', 'shadow-sm');
            document.getElementById('grid-view').classList.add('text-gray-500');
        });

        // Menu functionality
        function toggleMenu(event, menuId) {
            event.preventDefault();
            event.stopPropagation();

            // Close all other menus
            document.querySelectorAll('[id^="menu-"]').forEach(menu => {
                if (menu.id !== menuId) {
                    menu.classList.add('hidden');
                }
            });

            // Toggle current menu
            const menu = document.getElementById(menuId);
            menu.classList.toggle('hidden');
        }

        // Close menus when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('[onclick*="toggleMenu"]')) {
                document.querySelectorAll('[id^="menu-"]').forEach(menu => {
                    menu.classList.add('hidden');
                });
            }
        });

        // Form actions
        function duplicateForm(formId) {
            // Implementation for form duplication
            console.log('Duplicate form:', formId);
            // You can implement AJAX call here
        }

        function toggleFormStatus(formId, newStatus) {
            // Implementation for toggling form status
            console.log('Toggle form status:', formId, newStatus);
            // You can implement AJAX call here
        }
    </script>
</body>
</html>
