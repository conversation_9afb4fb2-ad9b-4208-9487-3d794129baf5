<x-app-layout>
    <!-- Google Forms Style Header -->
    <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-wpforms text-white text-lg"></i>
                        </div>
                        <div class="ml-3">
                            <h1 class="text-xl font-medium text-gray-900">Forms</h1>
                        </div>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                        <i class="fas fa-search text-lg"></i>
                    </button>
                    <button class="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                        <i class="fas fa-th text-lg"></i>
                    </button>
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-medium">{{ substr(Auth::user()->name, 0, 1) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="min-h-screen bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

            <!-- Start a new form section -->
            <div class="mb-8">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Start a new form</h2>
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    <!-- Blank Form -->
                    <a href="{{ route('forms.create') }}" class="group">
                        <div class="bg-white rounded-lg border border-gray-200 hover:border-purple-300 hover:shadow-md transition-all duration-200 p-4 text-center">
                            <div class="w-16 h-20 mx-auto mb-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-plus text-white text-2xl"></i>
                            </div>
                            <h3 class="text-sm font-medium text-gray-900 group-hover:text-purple-600">Blank</h3>
                        </div>
                    </a>

                    <!-- Contact Form Template -->
                    <a href="{{ route('forms.create') }}?template=contact" class="group">
                        <div class="bg-white rounded-lg border border-gray-200 hover:border-purple-300 hover:shadow-md transition-all duration-200 p-4 text-center">
                            <div class="w-16 h-20 mx-auto mb-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-envelope text-white text-xl"></i>
                            </div>
                            <h3 class="text-sm font-medium text-gray-900 group-hover:text-purple-600">Contact</h3>
                        </div>
                    </a>

                    <!-- Survey Template -->
                    <a href="{{ route('forms.create') }}?template=survey" class="group">
                        <div class="bg-white rounded-lg border border-gray-200 hover:border-purple-300 hover:shadow-md transition-all duration-200 p-4 text-center">
                            <div class="w-16 h-20 mx-auto mb-3 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-poll text-white text-xl"></i>
                            </div>
                            <h3 class="text-sm font-medium text-gray-900 group-hover:text-purple-600">Survey</h3>
                        </div>
                    </a>

                    <!-- Registration Template -->
                    <a href="{{ route('forms.create') }}?template=registration" class="group">
                        <div class="bg-white rounded-lg border border-gray-200 hover:border-purple-300 hover:shadow-md transition-all duration-200 p-4 text-center">
                            <div class="w-16 h-20 mx-auto mb-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-user-plus text-white text-xl"></i>
                            </div>
                            <h3 class="text-sm font-medium text-gray-900 group-hover:text-purple-600">Registration</h3>
                        </div>
                    </a>

                    <!-- Feedback Template -->
                    <a href="{{ route('forms.create') }}?template=feedback" class="group">
                        <div class="bg-white rounded-lg border border-gray-200 hover:border-purple-300 hover:shadow-md transition-all duration-200 p-4 text-center">
                            <div class="w-16 h-20 mx-auto mb-3 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-comment text-white text-xl"></i>
                            </div>
                            <h3 class="text-sm font-medium text-gray-900 group-hover:text-purple-600">Feedback</h3>
                        </div>
                    </a>

                    <!-- Order Form Template -->
                    <a href="{{ route('forms.create') }}?template=order" class="group">
                        <div class="bg-white rounded-lg border border-gray-200 hover:border-purple-300 hover:shadow-md transition-all duration-200 p-4 text-center">
                            <div class="w-16 h-20 mx-auto mb-3 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-shopping-cart text-white text-xl"></i>
                            </div>
                            <h3 class="text-sm font-medium text-gray-900 group-hover:text-purple-600">Order</h3>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Recent Forms Section -->
            <div class="mb-8">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-medium text-gray-900">Recent forms</h2>
                    @if(Auth::user()->forms->count() > 0)
                        <a href="{{ route('forms.index') }}" class="text-purple-600 hover:text-purple-700 text-sm font-medium">View all</a>
                    @endif
                </div>

                @if(Auth::user()->forms->count() > 0)
                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                        @foreach(Auth::user()->forms->take(10) as $form)
                            <div class="group relative">
                                <a href="{{ route('forms.builder', $form) }}" class="block">
                                    <div class="bg-white rounded-lg border border-gray-200 hover:border-purple-300 hover:shadow-md transition-all duration-200 p-4">
                                        <!-- Form Icon/Preview -->
                                        <div class="w-full h-32 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg mb-3 flex items-center justify-center relative overflow-hidden">
                                            <div class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-purple-600/10"></div>
                                            <i class="fas fa-wpforms text-purple-400 text-3xl relative z-10"></i>

                                            <!-- Status indicator -->
                                            <div class="absolute top-2 right-2">
                                                @if($form->is_active)
                                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                                @else
                                                    <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                                                @endif
                                            </div>
                                        </div>

                                        <!-- Form Info -->
                                        <div class="space-y-1">
                                            <h3 class="font-medium text-gray-900 text-sm truncate group-hover:text-purple-600">
                                                {{ $form->title }}
                                            </h3>
                                            <p class="text-xs text-gray-500">
                                                {{ $form->submissions->count() }} responses
                                            </p>
                                            <p class="text-xs text-gray-400">
                                                {{ $form->updated_at->diffForHumans() }}
                                            </p>
                                        </div>
                                    </div>
                                </a>

                                <!-- Quick Actions Menu -->
                                <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                    <div class="relative">
                                        <button class="p-1 text-gray-400 hover:text-gray-600 bg-white rounded-full shadow-sm" onclick="toggleMenu(event, 'menu-{{ $form->id }}')">
                                            <i class="fas fa-ellipsis-v text-xs"></i>
                                        </button>
                                        <div id="menu-{{ $form->id }}" class="hidden absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                                            <div class="py-1">
                                                <a href="{{ route('forms.builder', $form) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i class="fas fa-edit mr-2"></i>Edit
                                                </a>
                                                <a href="{{ route('forms.submissions', $form) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i class="fas fa-chart-bar mr-2"></i>Responses
                                                </a>
                                                <a href="{{ route('form.show', $form->slug) }}" target="_blank" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i class="fas fa-external-link-alt mr-2"></i>Preview
                                                </a>
                                                <div class="border-t border-gray-100"></div>
                                                <form action="{{ route('forms.destroy', $form) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this form?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                                        <i class="fas fa-trash mr-2"></i>Delete
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-wpforms text-gray-400 text-3xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Create your first form</h3>
                        <p class="text-gray-500 mb-6 max-w-sm mx-auto">Get started by creating a form from scratch or choose from our templates above.</p>
                        <a href="{{ route('forms.create') }}" class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition duration-200">
                            <i class="fas fa-plus mr-2"></i>Create your first form
                        </a>
                    </div>
                @endif
            </div>

        </div>
    </div>

    <!-- JavaScript for menu functionality -->
    <script>
        function toggleMenu(event, menuId) {
            event.preventDefault();
            event.stopPropagation();

            // Close all other menus
            document.querySelectorAll('[id^="menu-"]').forEach(menu => {
                if (menu.id !== menuId) {
                    menu.classList.add('hidden');
                }
            });

            // Toggle current menu
            const menu = document.getElementById(menuId);
            menu.classList.toggle('hidden');
        }

        // Close menus when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('[onclick*="toggleMenu"]')) {
                document.querySelectorAll('[id^="menu-"]').forEach(menu => {
                    menu.classList.add('hidden');
                });
            }
        });
    </script>
</x-app-layout>
