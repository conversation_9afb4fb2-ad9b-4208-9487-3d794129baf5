<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo e($form->title); ?> - Form Builder</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
</head>
<body class="font-sans antialiased bg-gray-50">

    <!-- Google Forms Style Header -->
    <div class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-3">
                <div class="flex items-center space-x-4">
                    <a href="<?php echo e(route('forms.index')); ?>" class="flex items-center hover:text-purple-600 transition-colors duration-200">
                        <div class="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-wpforms text-white text-lg"></i>
                        </div>
                        <div class="ml-3">
                            <h1 class="text-xl font-medium text-gray-900">Forms</h1>
                        </div>
                    </a>
                    <div class="text-gray-400">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                    <div class="text-gray-700 font-medium"><?php echo e($form->title); ?></div>
                </div>
                <div class="flex items-center space-x-3">
                    <button id="undo-btn" class="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100" disabled title="Undo">
                        <i class="fas fa-undo text-lg"></i>
                    </button>
                    <button id="redo-btn" class="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100" disabled title="Redo">
                        <i class="fas fa-redo text-lg"></i>
                    </button>
                    <div class="border-l border-gray-300 h-6 mx-2"></div>
                    <a href="<?php echo e(route('form.show', $form->slug)); ?>" target="_blank" class="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition duration-200">
                        <i class="fas fa-eye mr-2"></i>Preview
                    </a>
                    <button id="save-form" class="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                        <i class="fas fa-save mr-2"></i>Save
                    </button>
                    <button id="send-form" class="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                        <i class="fas fa-paper-plane mr-2"></i>Send
                    </button>
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-medium"><?php echo e(substr(Auth::user()->name, 0, 1)); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="min-h-screen">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

            <!-- Form Container -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">

                <!-- Form Header Section -->
                <div class="bg-gradient-to-r from-purple-600 to-purple-700 px-6 py-8">
                    <div class="space-y-4">
                        <!-- Form Title -->
                        <div>
                            <input type="text"
                                   id="form-title"
                                   value="<?php echo e($form->title); ?>"
                                   class="w-full bg-transparent text-white text-2xl font-medium placeholder-purple-200 border-none focus:outline-none focus:ring-0 p-0"
                                   style="background: none; box-shadow: none;">
                        </div>

                        <!-- Form Description -->
                        <div>
                            <textarea id="form-description"
                                      placeholder="Form description"
                                      rows="2"
                                      class="w-full bg-transparent text-purple-100 placeholder-purple-200 border-none focus:outline-none focus:ring-0 p-0 resize-none"
                                      style="background: none; box-shadow: none;"><?php echo e($form->description); ?></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Fields Container -->
            <div id="form-fields" class="space-y-4">
                <!-- Existing fields will be loaded here -->
            </div>

            <!-- Add Question Button -->
            <div class="mt-6 text-center">
                <div class="relative inline-block">
                    <button id="add-question-btn" class="bg-white hover:bg-gray-50 border border-gray-300 text-gray-700 font-medium py-3 px-6 rounded-full shadow-sm transition duration-200 flex items-center">
                        <i class="fas fa-plus mr-2"></i>Add Question
                    </button>

                    <!-- Field Type Selector (Hidden by default) -->
                    <div id="field-type-selector" class="hidden absolute top-full left-1/2 transform -translate-x-1/2 mt-2 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-10">
                        <div class="grid grid-cols-2 gap-2 w-80">
                            <button class="field-type-option p-3 text-left hover:bg-gray-50 rounded-lg transition duration-200" data-type="text">
                                <div class="flex items-center">
                                    <i class="fas fa-font text-gray-600 mr-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-900">Short Answer</div>
                                        <div class="text-xs text-gray-500">Single line text</div>
                                    </div>
                                </div>
                            </button>
                            <button class="field-type-option p-3 text-left hover:bg-gray-50 rounded-lg transition duration-200" data-type="textarea">
                                <div class="flex items-center">
                                    <i class="fas fa-align-left text-gray-600 mr-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-900">Paragraph</div>
                                        <div class="text-xs text-gray-500">Long text</div>
                                    </div>
                                </div>
                            </button>
                            <button class="field-type-option p-3 text-left hover:bg-gray-50 rounded-lg transition duration-200" data-type="select">
                                <div class="flex items-center">
                                    <i class="fas fa-list text-gray-600 mr-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-900">Multiple Choice</div>
                                        <div class="text-xs text-gray-500">Select one option</div>
                                    </div>
                                </div>
                            </button>
                            <button class="field-type-option p-3 text-left hover:bg-gray-50 rounded-lg transition duration-200" data-type="checkbox">
                                <div class="flex items-center">
                                    <i class="fas fa-check-square text-gray-600 mr-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-900">Checkboxes</div>
                                        <div class="text-xs text-gray-500">Select multiple</div>
                                    </div>
                                </div>
                            </button>
                            <button class="field-type-option p-3 text-left hover:bg-gray-50 rounded-lg transition duration-200" data-type="email">
                                <div class="flex items-center">
                                    <i class="fas fa-envelope text-gray-600 mr-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-900">Email</div>
                                        <div class="text-xs text-gray-500">Email address</div>
                                    </div>
                                </div>
                            </button>
                            <button class="field-type-option p-3 text-left hover:bg-gray-50 rounded-lg transition duration-200" data-type="number">
                                <div class="flex items-center">
                                    <i class="fas fa-hashtag text-gray-600 mr-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-900">Number</div>
                                        <div class="text-xs text-gray-500">Numeric input</div>
                                    </div>
                                </div>
                            </button>
                            <button class="field-type-option p-3 text-left hover:bg-gray-50 rounded-lg transition duration-200" data-type="date">
                                <div class="flex items-center">
                                    <i class="fas fa-calendar text-gray-600 mr-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-900">Date</div>
                                        <div class="text-xs text-gray-500">Date picker</div>
                                    </div>
                                </div>
                            </button>
                            <button class="field-type-option p-3 text-left hover:bg-gray-50 rounded-lg transition duration-200" data-type="radio">
                                <div class="flex items-center">
                                    <i class="fas fa-dot-circle text-gray-600 mr-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-900">Radio Buttons</div>
                                        <div class="text-xs text-gray-500">Single choice</div>
                                    </div>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Google Forms-like Form Builder -->
    <script>
        let formFields = <?php echo json_encode($form->fields, 15, 512) ?>;
        let fieldCounter = formFields.length;
        let hasUnsavedChanges = false;
        let undoStack = [];
        let redoStack = [];

        // Initialize form builder
        document.addEventListener('DOMContentLoaded', function() {
            initializeFormBuilder();
            loadExistingFields();
        });

        function initializeFormBuilder() {
            // Add event listeners
            document.getElementById('form-title').addEventListener('input', handleFormChange);
            document.getElementById('form-description').addEventListener('input', handleFormChange);
            document.getElementById('add-question-btn').addEventListener('click', toggleFieldTypeSelector);
            document.getElementById('save-form').addEventListener('click', saveForm);

            // Undo/Redo event listeners
            document.getElementById('undo-btn').addEventListener('click', undo);
            document.getElementById('redo-btn').addEventListener('click', redo);

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    if (e.key === 'z' && !e.shiftKey) {
                        e.preventDefault();
                        undo();
                    } else if ((e.key === 'z' && e.shiftKey) || e.key === 'y') {
                        e.preventDefault();
                        redo();
                    }
                }
            });

            // Field type options
            document.querySelectorAll('.field-type-option').forEach(btn => {
                btn.addEventListener('click', function() {
                    addField(this.dataset.type);
                    hideFieldTypeSelector();
                });
            });

            // Close field type selector when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('#add-question-btn') && !event.target.closest('#field-type-selector')) {
                    hideFieldTypeSelector();
                }
            });

            // Make form fields sortable
            new Sortable(document.getElementById('form-fields'), {
                animation: 150,
                ghostClass: 'sortable-ghost',
                onEnd: function(evt) {
                    updateFieldOrder();
                    handleFormChange();
                }
            });

            // Save initial state for undo/redo
            saveState();
        }

        function loadExistingFields() {
            formFields.forEach(field => {
                addExistingField(field);
            });
        }

        function addExistingField(field) {
            const fieldHtml = createFieldHtml(field.id, field.type, field.label, field.required, field.options || []);
            document.getElementById('form-fields').insertAdjacentHTML('beforeend', fieldHtml);
            addFieldEventListeners(field.id);
        }

        function toggleFieldTypeSelector() {
            const selector = document.getElementById('field-type-selector');
            selector.classList.toggle('hidden');
        }

        function hideFieldTypeSelector() {
            document.getElementById('field-type-selector').classList.add('hidden');
        }

        function addField(type, label = '', required = false, options = []) {
            fieldCounter++;
            const fieldId = `field_${fieldCounter}`;

            const newField = {
                id: fieldId,
                type: type,
                label: label || getDefaultLabel(type),
                name: `field_${fieldCounter}`,
                required: required,
                options: options,
                order: fieldCounter
            };

            formFields.push(newField);

            const fieldHtml = createFieldHtml(fieldId, type, newField.label, required, options);
            document.getElementById('form-fields').insertAdjacentHTML('beforeend', fieldHtml);

            // Add event listeners to new field
            addFieldEventListeners(fieldId);

            // Save state after adding field
            hasUnsavedChanges = true;
            saveState();

            // Focus on the label input
            setTimeout(() => {
                const labelInput = document.querySelector(`#${fieldId} .field-label-input`);
                if (labelInput && !label) {
                    labelInput.focus();
                    labelInput.select();
                }
            }, 100);
        }

        function createFieldHtml(fieldId, type, label, required, options) {
            const defaultLabel = label || getDefaultLabel(type);

            return `
                <div id="${fieldId}" class="field-container bg-white border border-gray-200 rounded-lg p-6 hover:border-purple-300 transition-all duration-200 mb-4">
                    <div class="flex justify-between items-start mb-4">
                        <div class="flex-1">
                            <input type="text"
                                   class="field-label-input text-lg font-medium text-gray-900 bg-transparent border-none focus:outline-none focus:ring-0 p-0 w-full"
                                   value="${defaultLabel}"
                                   placeholder="Question">
                        </div>
                        <div class="flex items-center space-x-2 ml-4">
                            <button class="duplicate-field text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100" title="Duplicate">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="delete-field text-gray-400 hover:text-red-600 p-2 rounded-full hover:bg-gray-100" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                            <div class="drag-handle cursor-move text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100" title="Drag to reorder">
                                <i class="fas fa-grip-vertical"></i>
                            </div>
                        </div>
                    </div>

                    <div class="field-preview mb-4">
                        ${createFieldPreview(type, options)}
                    </div>

                    <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                        <div class="flex items-center space-x-4">
                            <label class="flex items-center">
                                <input type="checkbox" class="field-required-checkbox text-purple-600 focus:ring-purple-500" ${required ? 'checked' : ''}>
                                <span class="ml-2 text-sm text-gray-600">Required</span>
                            </label>
                        </div>
                        <div class="flex items-center space-x-2">
                            <select class="field-type-select text-sm border border-gray-300 rounded-md px-3 py-1 focus:ring-purple-500 focus:border-purple-500">
                                <option value="text" ${type === 'text' ? 'selected' : ''}>Short Answer</option>
                                <option value="textarea" ${type === 'textarea' ? 'selected' : ''}>Paragraph</option>
                                <option value="email" ${type === 'email' ? 'selected' : ''}>Email</option>
                                <option value="number" ${type === 'number' ? 'selected' : ''}>Number</option>
                                <option value="date" ${type === 'date' ? 'selected' : ''}>Date</option>
                                <option value="select" ${type === 'select' ? 'selected' : ''}>Multiple Choice</option>
                                <option value="radio" ${type === 'radio' ? 'selected' : ''}>Radio</option>
                                <option value="checkbox" ${type === 'checkbox' ? 'selected' : ''}>Checkboxes</option>
                            </select>
                        </div>
                    </div>
                </div>
            `;
        }

        function createFieldPreview(type, options = []) {
            switch (type) {
                case 'text':
                case 'email':
                case 'number':
                    return '<input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500" placeholder="Your answer" disabled>';
                case 'textarea':
                    return '<textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500" rows="3" placeholder="Your answer" disabled></textarea>';
                case 'date':
                    return '<input type="date" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500" disabled>';
                case 'select':
                    const selectOptions = options.length ? options : ['Option 1', 'Option 2', 'Option 3'];
                    return `
                        <select class="px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500" disabled>
                            <option>Choose</option>
                            ${selectOptions.map(opt => `<option>${opt}</option>`).join('')}
                        </select>
                    `;
                case 'radio':
                    const radioOptions = options.length ? options : ['Option 1', 'Option 2', 'Option 3'];
                    return `
                        <div class="space-y-2">
                            ${radioOptions.map((opt, i) => `
                                <label class="flex items-center">
                                    <input type="radio" name="preview_radio_${fieldCounter}" class="text-purple-600 focus:ring-purple-500 mr-2" disabled>
                                    <span>${opt}</span>
                                </label>
                            `).join('')}
                        </div>
                    `;
                case 'checkbox':
                    const checkboxOptions = options.length ? options : ['Option 1', 'Option 2', 'Option 3'];
                    return `
                        <div class="space-y-2">
                            ${checkboxOptions.map(opt => `
                                <label class="flex items-center">
                                    <input type="checkbox" class="text-purple-600 focus:ring-purple-500 mr-2" disabled>
                                    <span>${opt}</span>
                                </label>
                            `).join('')}
                        </div>
                    `;
                default:
                    return '<input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500" placeholder="Your answer" disabled>';
            }
        }

        function getDefaultLabel(type) {
            const labels = {
                text: 'Short answer text',
                textarea: 'Long answer text',
                email: 'Email address',
                number: 'Number',
                date: 'Date',
                select: 'Multiple choice question',
                radio: 'Multiple choice question',
                checkbox: 'Checkbox question'
            };
            return labels[type] || 'Question';
        }

        function addFieldEventListeners(fieldId) {
            const container = document.getElementById(fieldId);

            // Label input
            container.querySelector('.field-label-input').addEventListener('input', function() {
                updateFieldData(fieldId, 'label', this.value);
            });

            // Required checkbox
            container.querySelector('.field-required-checkbox').addEventListener('change', function() {
                updateFieldData(fieldId, 'required', this.checked);
            });

            // Type select
            container.querySelector('.field-type-select').addEventListener('change', function() {
                updateFieldType(fieldId, this.value);
            });

            // Delete button
            container.querySelector('.delete-field').addEventListener('click', function() {
                deleteField(fieldId);
            });

            // Duplicate button
            container.querySelector('.duplicate-field').addEventListener('click', function() {
                duplicateField(fieldId);
            });
        }

        function updateFieldData(fieldId, property, value) {
            const field = formFields.find(f => f.id === fieldId);
            if (field) {
                field[property] = value;
                handleFormChange();
            }
        }

        function updateFieldType(fieldId, newType) {
            const field = formFields.find(f => f.id === fieldId);
            if (field) {
                field.type = newType;

                // Update preview
                const container = document.getElementById(fieldId);
                const preview = container.querySelector('.field-preview');
                preview.innerHTML = createFieldPreview(newType);

                handleFormChange();
            }
        }

        function deleteField(fieldId) {
            if (confirm('Are you sure you want to delete this question?')) {
                document.getElementById(fieldId).remove();
                formFields = formFields.filter(f => f.id !== fieldId);
                hasUnsavedChanges = true;
                saveState();
            }
        }

        function duplicateField(fieldId) {
            const field = formFields.find(f => f.id === fieldId);
            if (field) {
                addField(field.type, field.label + ' (Copy)', field.required, field.options);
            }
        }

        function updateFieldOrder() {
            const fieldElements = document.querySelectorAll('.field-container');
            fieldElements.forEach((element, index) => {
                const fieldId = element.id;
                const field = formFields.find(f => f.id === fieldId);
                if (field) {
                    field.order = index + 1;
                }
            });
            // Don't call saveState here as it's called by handleFormChange in the sortable onEnd event
        }

        function handleFormChange() {
            hasUnsavedChanges = true;
            saveState();
        }

        function saveState() {
            const currentState = {
                title: document.getElementById('form-title').value,
                description: document.getElementById('form-description').value,
                fields: [...formFields]
            };

            undoStack.push(JSON.stringify(currentState));

            // Limit undo stack to 50 items
            if (undoStack.length > 50) {
                undoStack.shift();
            }

            // Clear redo stack when new action is performed
            redoStack = [];

            updateUndoRedoButtons();
        }

        function undo() {
            if (undoStack.length <= 1) return; // Keep at least one state

            const currentState = undoStack.pop();
            redoStack.push(currentState);

            const previousState = JSON.parse(undoStack[undoStack.length - 1]);
            restoreState(previousState);

            updateUndoRedoButtons();
        }

        function redo() {
            if (redoStack.length === 0) return;

            const nextState = redoStack.pop();
            undoStack.push(nextState);

            const stateToRestore = JSON.parse(nextState);
            restoreState(stateToRestore);

            updateUndoRedoButtons();
        }

        function restoreState(state) {
            // Restore form title and description
            document.getElementById('form-title').value = state.title;
            document.getElementById('form-description').value = state.description;

            // Restore fields
            formFields = [...state.fields];

            // Clear and rebuild form fields
            const formFieldsContainer = document.getElementById('form-fields');
            formFieldsContainer.innerHTML = '';

            formFields.forEach(field => {
                addExistingField(field);
            });
        }

        function updateUndoRedoButtons() {
            const undoBtn = document.getElementById('undo-btn');
            const redoBtn = document.getElementById('redo-btn');

            undoBtn.disabled = undoStack.length <= 1;
            redoBtn.disabled = redoStack.length === 0;

            if (undoBtn.disabled) {
                undoBtn.classList.add('text-gray-300');
                undoBtn.classList.remove('text-gray-400', 'hover:text-gray-600');
            } else {
                undoBtn.classList.remove('text-gray-300');
                undoBtn.classList.add('text-gray-400', 'hover:text-gray-600');
            }

            if (redoBtn.disabled) {
                redoBtn.classList.add('text-gray-300');
                redoBtn.classList.remove('text-gray-400', 'hover:text-gray-600');
            } else {
                redoBtn.classList.remove('text-gray-300');
                redoBtn.classList.add('text-gray-400', 'hover:text-gray-600');
            }
        }

        function saveForm() {
            const title = document.getElementById('form-title').value.trim();
            const description = document.getElementById('form-description').value.trim();

            if (!title) {
                alert('Please enter a form title');
                return;
            }

            // Show loading state
            const saveBtn = document.getElementById('save-form');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
            saveBtn.disabled = true;

            // Update form basic info first
            const updateUrl = `<?php echo e(route('forms.update', $form)); ?>`;
            console.log('Update URL:', updateUrl);
            console.log('Request data:', { title, description });

            fetch(updateUrl, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    title: title,
                    description: description
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    hasUnsavedChanges = false;
                    showSuccessMessage('Form saved successfully!');
                    saveBtn.innerHTML = originalText;
                    saveBtn.disabled = false;
                } else {
                    throw new Error(data.message || 'Failed to update form');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorMessage('Error saving form: ' + error.message);
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;
            });
        }

        function showSuccessMessage(message) {
            // Create and show success notification
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            notification.innerHTML = `<i class="fas fa-check mr-2"></i>${message}`;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        function showErrorMessage(message) {
            // Create and show error notification
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            notification.innerHTML = `<i class="fas fa-exclamation-triangle mr-2"></i>${message}`;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // Warn about unsaved changes
        window.addEventListener('beforeunload', function(e) {
            if (hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    </script>
</body>
</html>
<?php /**PATH D:\Projects\form-builder\resources\views/forms/builder.blade.php ENDPATH**/ ?>