{"__meta": {"id": "01K0PSVTXNGF12EJMNXBDCG937", "datetime": "2025-07-21 15:33:00", "utime": **********.982133, "method": "GET", "uri": "/forms/form-testtttttt-GpuTUZ/builder", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 5, "start": **********.594048, "end": **********.982149, "duration": 0.38810086250305176, "duration_str": "388ms", "measures": [{"label": "Booting", "start": **********.594048, "relative_start": 0, "end": **********.772547, "relative_end": **********.772547, "duration": 0.****************, "duration_str": "178ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.772561, "relative_start": 0.****************, "end": **********.982151, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "210ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.795222, "relative_start": 0.*****************, "end": **********.798946, "relative_end": **********.798946, "duration": 0.0037238597869873047, "duration_str": "3.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.873151, "relative_start": 0.*****************, "end": **********.98017, "relative_end": **********.98017, "duration": 0.*****************, "duration_str": "107ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: forms.builder", "start": **********.875191, "relative_start": 0.****************, "end": **********.875191, "relative_end": **********.875191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.20.0", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "forms.builder", "param_count": null, "params": [], "start": **********.875166, "type": "blade", "hash": "bladeD:\\Projects\\form-builder\\resources\\views/forms/builder.blade.phpforms.builder", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fresources%2Fviews%2Fforms%2Fbuilder.blade.php&line=1", "ajax": false, "filename": "builder.blade.php", "line": "?"}}]}, "queries": {"count": 6, "nb_statements": 5, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.023940000000000006, "accumulated_duration_str": "23.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.812694, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "formdb", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'nmNq7LuX2ea8Wa1tD7RrtDHSN8YpvKknrrbPD9cV' limit 1", "type": "query", "params": [], "bindings": ["nmNq7LuX2ea8Wa1tD7RrtDHSN8YpvKknrrbPD9cV"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.819765, "duration": 0.019260000000000003, "duration_str": "19.26ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "formdb", "explain": null, "start_percent": 0, "width_percent": 80.451}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.84792, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "formdb", "explain": null, "start_percent": 80.451, "width_percent": 6.224}, {"sql": "select * from `forms` where `slug` = 'form-testtttttt-GpuTUZ' limit 1", "type": "query", "params": [], "bindings": ["form-testtttttt-GpuTUZ"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 965}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.8530838, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:60", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=60", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "60"}, "connection": "formdb", "explain": null, "start_percent": 86.675, "width_percent": 3.801}, {"sql": "select * from `form_fields` where `form_fields`.`form_id` in (5) order by `order` asc, `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/FormBuilderController.php", "file": "D:\\Projects\\form-builder\\app\\Http\\Controllers\\FormBuilderController.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.862946, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "FormBuilderController.php:17", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/FormBuilderController.php", "file": "D:\\Projects\\form-builder\\app\\Http\\Controllers\\FormBuilderController.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FHttp%2FControllers%2FFormBuilderController.php&line=17", "ajax": false, "filename": "FormBuilderController.php", "line": "17"}, "connection": "formdb", "explain": null, "start_percent": 90.476, "width_percent": 6.433}, {"sql": "select * from `conditional_rules` where `conditional_rules`.`form_id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/FormBuilderController.php", "file": "D:\\Projects\\form-builder\\app\\Http\\Controllers\\FormBuilderController.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\Projects\\form-builder\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.866236, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "FormBuilderController.php:17", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/FormBuilderController.php", "file": "D:\\Projects\\form-builder\\app\\Http\\Controllers\\FormBuilderController.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FHttp%2FControllers%2FFormBuilderController.php&line=17", "ajax": false, "filename": "FormBuilderController.php", "line": "17"}, "connection": "formdb", "explain": null, "start_percent": 96.909, "width_percent": 3.091}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Form": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FModels%2FForm.php&line=1", "ajax": false, "filename": "Form.php", "line": "?"}}}, "count": 2, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 2}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => update,\n  target => App\\Models\\Form(id=5),\n  result => true,\n  user => 2,\n  arguments => [0 => Object(App\\Models\\Form)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1474523356 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Form(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Form(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Form)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1474523356\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.858244, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/forms/form-testtttttt-GpuTUZ/builder", "action_name": "forms.builder", "controller_action": "App\\Http\\Controllers\\FormBuilderController@show", "uri": "GET forms/{form}/builder", "controller": "App\\Http\\Controllers\\FormBuilderController@show<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FHttp%2FControllers%2FFormBuilderController.php&line=13\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2Fform-builder%2Fapp%2FHttp%2FControllers%2FFormBuilderController.php&line=13\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/FormBuilderController.php:13-22</a>", "middleware": "web, auth", "duration": "388ms", "peak_memory": "26MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1089201393 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1089201393\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-860136277 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-860136277\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1154912480 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/forms</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ms;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1412 characters\">__stripe_mid=45fe3c7d-c6f2-457a-9c9f-a107d38d1d54978fc7; _ga=GA1.1.192285856.1746099575; _ga_69MPZE94D5=GS1.1.1746099574.1.1.1746099605.0.0.0; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndUUkRKeEd3elBPclVweVUrakhwcFE9PSIsInZhbHVlIjoiUHhxUmdma0t6RHltV2pCOWlEbmlOcUUrS2VBOCtRVjNrcklHYlpnc3lLL0dQeXlhV0VhNzh0Um5SakFGVitSdVh3bGw2WGg3MjM4NTU4aEhDQXlXRSt1MEtUN0xoQ2tHWFl2T2l3RDAwY0N6ZUlqQ3BSMDNrRnlCYnNSRnZEZG9wZlN0eVQ1SHE5dk5Nbk1CWlZ1bjByWXE1SFFjNVBiZHliU0U1MVgrbk1wcFdndzBrWis2VTcwRTdEMFJMcVdMNk9HSlVtTVJKZHNHTHhGZUEyQ29TbjQvMnJJSmFCT3AyUDJxRXFvQUp0Yz0iLCJtYWMiOiJlN2FmNmI4MWJjMDZlMjVmZWEyMjA3NDBkNGQxMTA2ODIzZjVkNjgzZTIwOTlmNTgyMWVkNTI1NjY3ZTAxZjc3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Imw2Y3U2STVnM2RmLzQzemowTHR1RVE9PSIsInZhbHVlIjoiblZpZW5pTDAraHZRR0E4Y1RNckNMMXRid2pLcFN0cXNYdW40RGVVaHpCSkJ5WUVLbENFQ2xWSTlHeEVPWnhmcHZUNFV5WURnLzBtZncwSWF5U1ZhdjNBaFdjSUJLVHd3VkduZDJaYm56aTQxVkk5MUZGTzF4SUJ1NHU5R29SWWwiLCJtYWMiOiI5NzE1M2UzNGY2YjU0YTMzMTM1NTU1ODhkNWExYjM2MzQ1ZGI1ODE5NjJiMzUxMDc5NzZkOTVkOTk0NDVjYzI5IiwidGFnIjoiIn0%3D; anggur_form_builder_session=eyJpdiI6IjhxWTZic0ZMQlZmb0ZnTjhoOE5wV3c9PSIsInZhbHVlIjoiWGZCN2RGZS9IdGZGYWI1eFFTa01aZExCRE8vUERQeVRGY1U0MDZvRW56ZXRZUjhQWW5lWTdjVXBRbm1lYnlibWU5Z21zeCtIRnZVRDhmc0E3b0RieWZXOWtsdFQ5Z0h3cFBpSTR1NXF0MFJNQ1orVDRQV0RWUGlVTlNOSjhqbVciLCJtYWMiOiI2YjRiMDdhMDI4YzZkYTczYjdhNTI3NTdhM2E0MzRkZjFjYTBiZjNlZjhkYmI3ODgwMjNiYmJlNzZmMjRhZjBjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1154912480\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_69MPZE94D5</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">n8tjAvFfC0SdTDeXFBvDP9ipLDa2OqtwPubmZtNX</span>\"\n  \"<span class=sf-dump-key>anggur_form_builder_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nmNq7LuX2ea8Wa1tD7RrtDHSN8YpvKknrrbPD9cV</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-813361198 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 15:33:00 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-813361198\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-150759667 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">n8tjAvFfC0SdTDeXFBvDP9ipLDa2OqtwPubmZtNX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/forms</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-150759667\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/forms/form-testtttttt-GpuTUZ/builder", "action_name": "forms.builder", "controller_action": "App\\Http\\Controllers\\FormBuilderController@show"}, "badge": null}}